/**
 * إدارة فواتير الموردين
 * يحتوي على جميع وظائف إنشاء وإدارة فواتير الموردين مع الحسابات المتقدمة
 */

class InvoicesManager {
    constructor() {
        this.invoices = [];
        this.currentSupplier = null;
        this.currentInvoiceItems = [];
        this.init();
    }

    /**
     * تهيئة مدير الفواتير
     */
    init() {
        this.loadInvoices();
        this.bindEvents();
        this.renderSavedInvoices();
        this.populateSupplierSelect();
        this.setDefaultDate();
    }

    /**
     * تحميل الفواتير من التخزين
     */
    loadInvoices() {
        this.invoices = storage.loadItems('invoices');
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // تحديد التاريخ
        const invoiceDateInput = document.getElementById('invoice-date');
        if (invoiceDateInput) {
            invoiceDateInput.addEventListener('change', () => this.populateSupplierSelect());
        }

        // اختيار المورد
        const supplierSelect = document.getElementById('invoice-supplier-select');
        if (supplierSelect) {
            supplierSelect.addEventListener('change', (e) => this.selectSupplier(e.target.value));
        }

        // زر استخراج جميع الفواتير
        const generateAllBtn = document.getElementById('generate-all-invoices');
        if (generateAllBtn) {
            generateAllBtn.addEventListener('click', () => this.generateAllInvoices());
        }

        // زر طباعة الفاتورة
        const printInvoiceBtn = document.getElementById('print-invoice');
        if (printInvoiceBtn) {
            printInvoiceBtn.addEventListener('click', () => this.printCurrentInvoice());
        }

        // زر حفظ الفاتورة
        const saveInvoiceBtn = document.getElementById('save-invoice');
        if (saveInvoiceBtn) {
            saveInvoiceBtn.addEventListener('click', () => this.saveCurrentInvoice());
        }
    }

    /**
     * تعيين التاريخ الافتراضي
     */
    setDefaultDate() {
        const invoiceDateInput = document.getElementById('invoice-date');
        if (invoiceDateInput) {
            invoiceDateInput.value = Utils.getCurrentDate();
        }
    }

    /**
     * ملء قائمة الموردين حسب التاريخ المحدد
     */
    populateSupplierSelect() {
        const invoiceDate = document.getElementById('invoice-date').value;
        if (!invoiceDate) return;

        const supplierSelect = document.getElementById('invoice-supplier-select');
        if (!supplierSelect) return;

        // الحصول على المشتريات في التاريخ المحدد
        const datePurchases = storage.filterByDate('purchases', invoiceDate, invoiceDate);
        
        // تجميع الموردين
        const suppliers = [...new Set(datePurchases.map(p => p.supplierName))];

        supplierSelect.innerHTML = '<option value="">اختر المورد</option>' +
            suppliers.map(supplier => `<option value="${supplier}">${supplier}</option>`).join('');
    }

    /**
     * اختيار مورد وإنشاء فاتورته
     */
    selectSupplier(supplierName) {
        if (!supplierName) {
            this.hideInvoiceForm();
            return;
        }

        this.currentSupplier = supplierName;
        this.generateSupplierInvoice(supplierName);
        this.showInvoiceForm();
    }

    /**
     * إنشاء فاتورة مورد
     */
    generateSupplierInvoice(supplierName) {
        const invoiceDate = document.getElementById('invoice-date').value;
        if (!invoiceDate) return;

        // الحصول على مشتريات المورد في التاريخ المحدد
        const supplierPurchases = storage.filterByDate('purchases', invoiceDate, invoiceDate)
            .filter(p => p.supplierName === supplierName);

        if (supplierPurchases.length === 0) {
            Utils.showError('لا توجد مشتريات لهذا المورد في التاريخ المحدد');
            return;
        }

        // تجميع المشتريات حسب المنتج ونوع الصندوق
        const groupedItems = {};
        supplierPurchases.forEach(purchase => {
            const key = `${purchase.productType}-${purchase.boxType}`;
            if (!groupedItems[key]) {
                groupedItems[key] = {
                    productType: purchase.productType,
                    boxType: purchase.boxType,
                    boxTypeId: purchase.boxTypeId,
                    boxCount: 0,
                    netWeight: 0,
                    pricePerKg: purchase.pricePerKg,
                    purchases: []
                };
            }
            
            groupedItems[key].boxCount += parseInt(purchase.boxCount) || 0;
            groupedItems[key].netWeight += parseFloat(purchase.netWeight) || 0;
            groupedItems[key].purchases.push(purchase);
        });

        this.currentInvoiceItems = Object.values(groupedItems);
        this.renderInvoiceItems();
        this.calculateInvoiceTotals();
    }

    /**
     * عرض عناصر الفاتورة
     */
    renderInvoiceItems() {
        const invoiceItemsContainer = document.getElementById('invoice-items');
        const supplierNameElement = document.getElementById('invoice-supplier-name');
        
        if (supplierNameElement) {
            supplierNameElement.textContent = `فاتورة المورد: ${this.currentSupplier}`;
        }

        if (!invoiceItemsContainer) return;

        if (this.currentInvoiceItems.length === 0) {
            invoiceItemsContainer.innerHTML = '<p class="text-center">لا توجد عناصر في الفاتورة</p>';
            return;
        }

        invoiceItemsContainer.innerHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>البضاعة</th>
                        <th>نوع الصندوق</th>
                        <th>عدد الصناديق</th>
                        <th>الوزن الصافي (كغ)</th>
                        <th>السعر/كغ (د.ت)</th>
                        <th>المبلغ (د.ت)</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.currentInvoiceItems.map((item, index) => {
                        const totalAmount = item.netWeight * item.pricePerKg;
                        return `
                            <tr>
                                <td>${item.productType}</td>
                                <td>${item.boxType}</td>
                                <td>
                                    <input type="number" 
                                           value="${item.boxCount}" 
                                           min="1" 
                                           class="form-control form-control-sm"
                                           onchange="invoicesManager.updateItemBoxCount(${index}, this.value)">
                                </td>
                                <td>
                                    <input type="number" 
                                           value="${Utils.formatNumber(item.netWeight)}" 
                                           min="0" 
                                           step="0.1"
                                           class="form-control form-control-sm"
                                           onchange="invoicesManager.updateItemWeight(${index}, this.value)">
                                </td>
                                <td>
                                    <input type="number" 
                                           value="${Utils.formatNumber(item.pricePerKg)}" 
                                           min="0" 
                                           step="0.001"
                                           class="form-control form-control-sm"
                                           onchange="invoicesManager.updateItemPrice(${index}, this.value)">
                                </td>
                                <td class="text-center">
                                    <strong>${Utils.formatCurrency(totalAmount)}</strong>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-danger" 
                                            onclick="invoicesManager.removeInvoiceItem(${index})"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
    }

    /**
     * تحديث عدد الصناديق لعنصر
     */
    updateItemBoxCount(index, newCount) {
        if (this.currentInvoiceItems[index]) {
            this.currentInvoiceItems[index].boxCount = parseInt(newCount) || 0;
            this.calculateInvoiceTotals();
        }
    }

    /**
     * تحديث الوزن لعنصر
     */
    updateItemWeight(index, newWeight) {
        if (this.currentInvoiceItems[index]) {
            this.currentInvoiceItems[index].netWeight = parseFloat(newWeight) || 0;
            this.calculateInvoiceTotals();
        }
    }

    /**
     * تحديث السعر لعنصر
     */
    updateItemPrice(index, newPrice) {
        if (this.currentInvoiceItems[index]) {
            this.currentInvoiceItems[index].pricePerKg = parseFloat(newPrice) || 0;
            this.renderInvoiceItems();
            this.calculateInvoiceTotals();
        }
    }

    /**
     * حذف عنصر من الفاتورة
     */
    removeInvoiceItem(index) {
        if (this.currentInvoiceItems[index]) {
            this.currentInvoiceItems.splice(index, 1);
            this.renderInvoiceItems();
            this.calculateInvoiceTotals();
        }
    }

    /**
     * حساب إجماليات الفاتورة
     */
    calculateInvoiceTotals() {
        const settings = storage.loadData()?.settings || storage.getDefaultSettings();
        const invoiceCalculation = calculator.calculateSupplierInvoice(this.currentInvoiceItems, settings);

        // تحديث عرض الحسابات
        document.getElementById('gross-amount').textContent = Utils.formatCurrency(invoiceCalculation.grossAmount);
        document.getElementById('four-percent').textContent = Utils.formatCurrency(invoiceCalculation.fourPercentAmount);
        document.getElementById('seven-percent').textContent = Utils.formatCurrency(invoiceCalculation.sevenPercentAmount);
        document.getElementById('loading-cost').textContent = Utils.formatCurrency(invoiceCalculation.loadingCost);
        document.getElementById('net-amount').textContent = Utils.formatCurrency(invoiceCalculation.netAmount);

        this.currentInvoiceCalculation = invoiceCalculation;
    }

    /**
     * عرض نموذج الفاتورة
     */
    showInvoiceForm() {
        const formContainer = document.getElementById('invoice-form-container');
        if (formContainer) {
            formContainer.style.display = 'block';
        }
    }

    /**
     * إخفاء نموذج الفاتورة
     */
    hideInvoiceForm() {
        const formContainer = document.getElementById('invoice-form-container');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        this.currentSupplier = null;
        this.currentInvoiceItems = [];
    }

    /**
     * طباعة الفاتورة الحالية
     */
    printCurrentInvoice() {
        if (!this.currentSupplier || !this.currentInvoiceCalculation) {
            Utils.showError('لا توجد فاتورة لطباعتها');
            return;
        }

        const invoiceDate = document.getElementById('invoice-date').value;
        const settings = storage.loadData()?.settings || storage.getDefaultSettings();

        const invoiceHTML = this.generateInvoiceHTML(
            this.currentSupplier,
            invoiceDate,
            this.currentInvoiceItems,
            this.currentInvoiceCalculation,
            settings
        );

        Utils.printHTML(invoiceHTML, `فاتورة_${this.currentSupplier}_${invoiceDate}`);
    }

    /**
     * حفظ الفاتورة الحالية
     */
    saveCurrentInvoice() {
        if (!this.currentSupplier || !this.currentInvoiceCalculation) {
            Utils.showError('لا توجد فاتورة لحفظها');
            return;
        }

        const invoiceDate = document.getElementById('invoice-date').value;
        
        const invoice = {
            id: storage.generateId(this.invoices),
            invoiceNumber: Utils.generateInvoiceNumber('SUP', new Date(invoiceDate)),
            supplierName: this.currentSupplier,
            invoiceDate: invoiceDate,
            items: this.currentInvoiceItems,
            grossAmount: this.currentInvoiceCalculation.grossAmount,
            fourPercentAmount: this.currentInvoiceCalculation.fourPercentAmount,
            sevenPercentAmount: this.currentInvoiceCalculation.sevenPercentAmount,
            loadingCost: this.currentInvoiceCalculation.loadingCost,
            netAmount: this.currentInvoiceCalculation.netAmount,
            status: 'محفوظة',
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('invoices', invoice)) {
            this.loadInvoices();
            this.renderSavedInvoices();
            Utils.showSuccess('تم حفظ الفاتورة بنجاح');
        } else {
            Utils.showError('فشل في حفظ الفاتورة');
        }
    }

    /**
     * إنشاء جميع الفواتير للتاريخ المحدد
     */
    generateAllInvoices() {
        const invoiceDate = document.getElementById('invoice-date').value;
        if (!invoiceDate) {
            Utils.showError('يرجى تحديد التاريخ أولاً');
            return;
        }

        const datePurchases = storage.filterByDate('purchases', invoiceDate, invoiceDate);
        if (datePurchases.length === 0) {
            Utils.showError('لا توجد مشتريات في التاريخ المحدد');
            return;
        }

        const suppliers = [...new Set(datePurchases.map(p => p.supplierName))];
        const settings = storage.loadData()?.settings || storage.getDefaultSettings();

        let allInvoicesHTML = `
            <div class="all-invoices">
                <div class="invoices-header">
                    <h1>${settings.marketName}</h1>
                    <h2>نقطة بيع عدد ${settings.posNumber}</h2>
                    <h3>فواتير الموردين - ${Utils.formatDate(invoiceDate)}</h3>
                    <hr>
                </div>
        `;

        suppliers.forEach((supplier, index) => {
            // إنشاء فاتورة لكل مورد
            const supplierPurchases = datePurchases.filter(p => p.supplierName === supplier);
            
            // تجميع المشتريات
            const groupedItems = {};
            supplierPurchases.forEach(purchase => {
                const key = `${purchase.productType}-${purchase.boxType}`;
                if (!groupedItems[key]) {
                    groupedItems[key] = {
                        productType: purchase.productType,
                        boxType: purchase.boxType,
                        boxTypeId: purchase.boxTypeId,
                        boxCount: 0,
                        netWeight: 0,
                        pricePerKg: purchase.pricePerKg
                    };
                }
                
                groupedItems[key].boxCount += parseInt(purchase.boxCount) || 0;
                groupedItems[key].netWeight += parseFloat(purchase.netWeight) || 0;
            });

            const items = Object.values(groupedItems);
            const calculation = calculator.calculateSupplierInvoice(items, settings);

            allInvoicesHTML += this.generateInvoiceHTML(supplier, invoiceDate, items, calculation, settings, false);
            
            if (index < suppliers.length - 1) {
                allInvoicesHTML += '<div style="page-break-after: always;"></div>';
            }
        });

        allInvoicesHTML += '</div>';

        Utils.printHTML(allInvoicesHTML, `جميع_الفواتير_${invoiceDate}`);
    }

    /**
     * إنشاء HTML للفاتورة
     */
    generateInvoiceHTML(supplierName, invoiceDate, items, calculation, settings, includeHeader = true) {
        return `
            <div class="supplier-invoice">
                ${includeHeader ? `
                    <div class="invoice-header">
                        <h1>${settings.marketName}</h1>
                        <h2>نقطة بيع عدد ${settings.posNumber}</h2>
                        <p>المالك: ${settings.ownerName}</p>
                        <hr>
                    </div>
                ` : ''}
                
                <div class="invoice-info">
                    <h3>فاتورة المورد: ${supplierName}</h3>
                    <p><strong>التاريخ:</strong> ${Utils.formatDate(invoiceDate)}</p>
                    <p><strong>رقم الفاتورة:</strong> ${Utils.generateInvoiceNumber('SUP', new Date(invoiceDate))}</p>
                </div>

                <div class="invoice-items">
                    <table class="invoice-table">
                        <thead>
                            <tr>
                                <th>البضاعة</th>
                                <th>نوع الصندوق</th>
                                <th>عدد الصناديق</th>
                                <th>الوزن الصافي (كغ)</th>
                                <th>السعر/كغ (د.ت)</th>
                                <th>المبلغ (د.ت)</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${items.map(item => {
                                const totalAmount = item.netWeight * item.pricePerKg;
                                return `
                                    <tr>
                                        <td>${item.productType}</td>
                                        <td>${item.boxType}</td>
                                        <td>${item.boxCount}</td>
                                        <td>${Utils.formatNumber(item.netWeight)}</td>
                                        <td>${Utils.formatCurrency(item.pricePerKg)}</td>
                                        <td>${Utils.formatCurrency(totalAmount)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-calculations">
                    <table class="calculations-table">
                        <tr>
                            <td>المبلغ الخام:</td>
                            <td>${Utils.formatCurrency(calculation.grossAmount)}</td>
                        </tr>
                        <tr>
                            <td>4% من الخام:</td>
                            <td>${Utils.formatCurrency(calculation.fourPercentAmount)}</td>
                        </tr>
                        <tr>
                            <td>7% من الخام:</td>
                            <td>${Utils.formatCurrency(calculation.sevenPercentAmount)}</td>
                        </tr>
                        <tr>
                            <td>الحمولة:</td>
                            <td>${Utils.formatCurrency(calculation.loadingCost)}</td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>الصافي:</strong></td>
                            <td><strong>${Utils.formatCurrency(calculation.netAmount)}</strong></td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-footer">
                    <p>توقيع المورد: ________________</p>
                    <p>توقيع المسؤول: ________________</p>
                    <p class="print-time">طُبعت في: ${Utils.formatDateTime(Utils.getCurrentDateTime())}</p>
                </div>
            </div>

            <style>
                .supplier-invoice {
                    max-width: 800px;
                    margin: 0 auto 40px auto;
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    padding: 20px;
                    border: 1px solid #ddd;
                }
                .invoice-header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .invoice-header h1 {
                    font-size: 24px;
                    margin-bottom: 10px;
                }
                .invoice-header h2 {
                    font-size: 18px;
                    margin-bottom: 5px;
                }
                .invoice-info {
                    margin-bottom: 30px;
                }
                .invoice-info h3 {
                    font-size: 20px;
                    margin-bottom: 10px;
                    color: #333;
                }
                .invoice-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }
                .invoice-table th,
                .invoice-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                .invoice-table th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .calculations-table {
                    width: 300px;
                    margin-left: auto;
                    border-collapse: collapse;
                }
                .calculations-table td {
                    padding: 5px 10px;
                    border-bottom: 1px dotted #ccc;
                }
                .calculations-table .total-row td {
                    border-top: 2px solid #000;
                    border-bottom: 2px solid #000;
                    font-size: 18px;
                }
                .invoice-footer {
                    margin-top: 40px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .print-time {
                    font-size: 12px;
                    color: #666;
                }
                @media print {
                    .supplier-invoice {
                        border: none;
                        margin: 0;
                        padding: 0;
                    }
                }
            </style>
        `;
    }

    /**
     * عرض الفواتير المحفوظة
     */
    renderSavedInvoices() {
        const tbody = document.getElementById('saved-invoices-tbody');
        if (!tbody) return;

        if (this.invoices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد فواتير محفوظة</td>
                </tr>
            `;
            return;
        }

        // ترتيب الفواتير حسب التاريخ (الأحدث أولاً)
        const sortedInvoices = [...this.invoices].sort((a, b) => 
            new Date(b.createdAt) - new Date(a.createdAt)
        );

        tbody.innerHTML = sortedInvoices.map(invoice => `
            <tr>
                <td>
                    <strong>${invoice.invoiceNumber}</strong>
                </td>
                <td>
                    ${Utils.formatDate(invoice.invoiceDate)}
                </td>
                <td>
                    <strong>${invoice.supplierName}</strong>
                </td>
                <td class="text-center">
                    <strong class="text-success">${Utils.formatCurrency(invoice.netAmount)}</strong>
                </td>
                <td class="text-center">
                    <span class="status-badge status-${invoice.status === 'محفوظة' ? 'paid' : 'unpaid'}">
                        ${invoice.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="invoicesManager.viewInvoiceDetails('${invoice.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn" onclick="invoicesManager.printSavedInvoice('${invoice.id}')" title="طباعة" style="background-color: var(--info-color);">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn delete" onclick="invoicesManager.deleteSavedInvoice('${invoice.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * عرض تفاصيل فاتورة محفوظة
     */
    viewInvoiceDetails(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const detailsHTML = `
            <div class="invoice-details">
                <div class="invoice-header">
                    <h2><i class="fas fa-file-invoice"></i> تفاصيل الفاتورة</h2>
                    <h3>رقم الفاتورة: ${invoice.invoiceNumber}</h3>
                </div>

                <div class="invoice-info">
                    <table class="info-table">
                        <tr>
                            <td><strong>المورد:</strong></td>
                            <td>${invoice.supplierName}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الفاتورة:</strong></td>
                            <td>${Utils.formatDate(invoice.invoiceDate)}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>${Utils.formatDateTime(invoice.createdAt)}</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>${invoice.status}</td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-items">
                    <h4>عناصر الفاتورة</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>البضاعة</th>
                                <th>نوع الصندوق</th>
                                <th>عدد الصناديق</th>
                                <th>الوزن الصافي</th>
                                <th>السعر/كغ</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => {
                                const totalAmount = item.netWeight * item.pricePerKg;
                                return `
                                    <tr>
                                        <td>${item.productType}</td>
                                        <td>${item.boxType}</td>
                                        <td>${item.boxCount}</td>
                                        <td>${Utils.formatNumber(item.netWeight)} كغ</td>
                                        <td>${Utils.formatCurrency(item.pricePerKg)}</td>
                                        <td>${Utils.formatCurrency(totalAmount)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-calculations">
                    <h4>الحسابات</h4>
                    <table class="calculations-table">
                        <tr>
                            <td>المبلغ الخام:</td>
                            <td>${Utils.formatCurrency(invoice.grossAmount)}</td>
                        </tr>
                        <tr>
                            <td>4% من الخام:</td>
                            <td>${Utils.formatCurrency(invoice.fourPercentAmount)}</td>
                        </tr>
                        <tr>
                            <td>7% من الخام:</td>
                            <td>${Utils.formatCurrency(invoice.sevenPercentAmount)}</td>
                        </tr>
                        <tr>
                            <td>الحمولة:</td>
                            <td>${Utils.formatCurrency(invoice.loadingCost)}</td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>الصافي:</strong></td>
                            <td><strong>${Utils.formatCurrency(invoice.netAmount)}</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل_الفاتورة_${invoice.invoiceNumber}`);
    }

    /**
     * طباعة فاتورة محفوظة
     */
    printSavedInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const settings = storage.loadData()?.settings || storage.getDefaultSettings();
        const invoiceHTML = this.generateInvoiceHTML(
            invoice.supplierName,
            invoice.invoiceDate,
            invoice.items,
            {
                grossAmount: invoice.grossAmount,
                fourPercentAmount: invoice.fourPercentAmount,
                sevenPercentAmount: invoice.sevenPercentAmount,
                loadingCost: invoice.loadingCost,
                netAmount: invoice.netAmount
            },
            settings
        );

        Utils.printHTML(invoiceHTML, `فاتورة_${invoice.supplierName}_${invoice.invoiceDate}`);
    }

    /**
     * حذف فاتورة محفوظة
     */
    deleteSavedInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        Utils.showConfirmation(
            `هل أنت متأكد من حذف الفاتورة رقم "${invoice.invoiceNumber}"؟\nالمورد: ${invoice.supplierName}`,
            () => {
                if (storage.deleteItem('invoices', invoiceId)) {
                    this.loadInvoices();
                    this.renderSavedInvoices();
                    Utils.showSuccess('تم حذف الفاتورة بنجاح');
                } else {
                    Utils.showError('فشل في حذف الفاتورة');
                }
            }
        );
    }

    /**
     * تصدير الفواتير إلى CSV
     */
    exportInvoices() {
        const invoicesData = this.invoices.map(invoice => ({
            'رقم الفاتورة': invoice.invoiceNumber,
            'المورد': invoice.supplierName,
            'تاريخ الفاتورة': Utils.formatDate(invoice.invoiceDate),
            'المبلغ الخام (د.ت)': invoice.grossAmount,
            '4% من الخام (د.ت)': invoice.fourPercentAmount,
            '7% من الخام (د.ت)': invoice.sevenPercentAmount,
            'الحمولة (د.ت)': invoice.loadingCost,
            'المبلغ الصافي (د.ت)': invoice.netAmount,
            'الحالة': invoice.status,
            'تاريخ الإنشاء': Utils.formatDateTime(invoice.createdAt)
        }));

        Utils.exportToCSV(invoicesData, `فواتير_الموردين_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * تحديث عرض الفواتير
     */
    updateInvoicesDisplay() {
        this.renderSavedInvoices();
    }
}

// إنشاء مثيل عام لمدير الفواتير
const invoicesManager = new InvoicesManager();
