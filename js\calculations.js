/**
 * نظام الحسابات المتقدم للسوق
 * يحتوي على جميع العمليات الحسابية المطلوبة
 */

class CalculationEngine {
    constructor() {
        this.settings = storage.loadData()?.settings || storage.getDefaultSettings();
    }

    /**
     * حساب الوزن الصافي بدقة
     */
    calculateNetWeight(grossWeight, boxType, boxCount = 1) {
        const gross = this.parseNumber(grossWeight);
        const emptyWeight = this.parseNumber(boxType.emptyWeight);
        const count = parseInt(boxCount) || 1;
        
        if (gross <= 0) return 0;
        
        // للصناديق التي يُدخل وزنها يدوياً
        if (boxType.name === 'Carton') {
            // يجب إدخال الوزن الفارغ يدوياً
            return Math.max(0, gross - (emptyWeight * count));
        }
        
        const netWeight = gross - (emptyWeight * count);
        return Math.max(0, netWeight);
    }

    /**
     * حساب المبلغ الإجمالي
     */
    calculateTotalAmount(netWeight, pricePerKg) {
        const weight = this.parseNumber(netWeight);
        const price = this.parseNumber(pricePerKg);
        
        return weight * price;
    }

    /**
     * حساب الرهن (حمولة الصناديق)
     */
    calculateDeposit(boxType, boxCount, netWeight = 0) {
        const count = parseInt(boxCount) || 1;
        const loadingPrice = this.parseNumber(boxType.loadingPrice);
        
        // حالة خاصة: بلا حمولة
        if (boxType.name === 'بلا حمولة') {
            const weight = this.parseNumber(netWeight);
            return weight * 10; // الوزن الصافي × 10
        }
        
        return count * loadingPrice;
    }

    /**
     * حساب فاتورة المورد الكاملة
     */
    calculateSupplierInvoice(items, settings = null) {
        const config = settings || this.settings;
        const fourPercent = this.parseNumber(config.fourPercent) || 4;
        const sevenPercent = this.parseNumber(config.sevenPercent) || 7;
        
        let grossAmount = 0;
        let totalLoadingCost = 0;
        
        // حساب المبلغ الخام وتكلفة الحمولة
        items.forEach(item => {
            const itemGross = this.calculateTotalAmount(item.netWeight, item.pricePerKg);
            grossAmount += itemGross;
            
            const boxType = this.getBoxTypeById(item.boxTypeId);
            if (boxType) {
                totalLoadingCost += this.calculateDeposit(boxType, item.boxCount, item.netWeight);
            }
        });
        
        // حساب النسب المئوية
        const fourPercentAmount = (grossAmount * fourPercent) / 100;
        const sevenPercentAmount = (grossAmount * sevenPercent) / 100;
        
        // حساب المبلغ الصافي
        const netAmount = grossAmount - fourPercentAmount - sevenPercentAmount - totalLoadingCost;
        
        return {
            grossAmount: this.roundNumber(grossAmount),
            fourPercentAmount: this.roundNumber(fourPercentAmount),
            sevenPercentAmount: this.roundNumber(sevenPercentAmount),
            loadingCost: this.roundNumber(totalLoadingCost),
            netAmount: this.roundNumber(netAmount),
            items: items.map(item => ({
                ...item,
                totalAmount: this.roundNumber(this.calculateTotalAmount(item.netWeight, item.pricePerKg))
            }))
        };
    }

    /**
     * حساب إحصائيات المبيعات
     */
    calculateSalesStatistics(purchases, startDate = null, endDate = null) {
        let filteredPurchases = purchases;
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            end.setHours(23, 59, 59, 999);
            
            filteredPurchases = purchases.filter(purchase => {
                const purchaseDate = new Date(purchase.createdAt);
                return purchaseDate >= start && purchaseDate <= end;
            });
        }
        
        const stats = {
            totalSales: 0,
            totalWeight: 0,
            totalBoxes: 0,
            totalDeposits: 0,
            purchaseCount: filteredPurchases.length,
            averageSale: 0,
            topProducts: {},
            topSuppliers: {},
            paymentStatus: {
                paid: 0,
                partial: 0,
                unpaid: 0
            }
        };
        
        filteredPurchases.forEach(purchase => {
            const amount = this.parseNumber(purchase.totalAmount);
            const weight = this.parseNumber(purchase.netWeight);
            const boxes = parseInt(purchase.boxCount) || 0;
            const deposit = this.parseNumber(purchase.deposit);
            
            stats.totalSales += amount;
            stats.totalWeight += weight;
            stats.totalBoxes += boxes;
            stats.totalDeposits += deposit;
            
            // إحصائيات المنتجات
            const product = purchase.productType;
            if (!stats.topProducts[product]) {
                stats.topProducts[product] = {
                    name: product,
                    totalSales: 0,
                    totalWeight: 0,
                    count: 0
                };
            }
            stats.topProducts[product].totalSales += amount;
            stats.topProducts[product].totalWeight += weight;
            stats.topProducts[product].count += 1;
            
            // إحصائيات الموردين
            const supplier = purchase.supplierName;
            if (!stats.topSuppliers[supplier]) {
                stats.topSuppliers[supplier] = {
                    name: supplier,
                    totalSales: 0,
                    totalWeight: 0,
                    count: 0
                };
            }
            stats.topSuppliers[supplier].totalSales += amount;
            stats.topSuppliers[supplier].totalWeight += weight;
            stats.topSuppliers[supplier].count += 1;
            
            // حالة الدفع
            const paymentStatus = purchase.paymentStatus || 'unpaid';
            if (stats.paymentStatus[paymentStatus] !== undefined) {
                stats.paymentStatus[paymentStatus] += amount;
            }
        });
        
        // حساب المتوسط
        stats.averageSale = stats.purchaseCount > 0 ? stats.totalSales / stats.purchaseCount : 0;
        
        // تحويل الكائنات إلى مصفوفات مرتبة
        stats.topProducts = Object.values(stats.topProducts)
            .sort((a, b) => b.totalSales - a.totalSales);
        
        stats.topSuppliers = Object.values(stats.topSuppliers)
            .sort((a, b) => b.totalSales - a.totalSales);
        
        // تقريب الأرقام
        stats.totalSales = this.roundNumber(stats.totalSales);
        stats.totalWeight = this.roundNumber(stats.totalWeight);
        stats.totalDeposits = this.roundNumber(stats.totalDeposits);
        stats.averageSale = this.roundNumber(stats.averageSale);
        
        Object.keys(stats.paymentStatus).forEach(key => {
            stats.paymentStatus[key] = this.roundNumber(stats.paymentStatus[key]);
        });
        
        return stats;
    }

    /**
     * حساب المخزون المتبقي
     */
    calculateRemainingInventory() {
        const purchases = storage.loadItems('purchases');
        const sales = storage.loadItems('sales') || []; // إذا كان هناك نظام مبيعات
        
        const inventory = {};
        
        // إضافة المشتريات
        purchases.forEach(purchase => {
            const key = `${purchase.supplierName}-${purchase.productType}`;
            if (!inventory[key]) {
                inventory[key] = {
                    supplierName: purchase.supplierName,
                    productType: purchase.productType,
                    totalBoxes: 0,
                    totalWeight: 0,
                    totalValue: 0,
                    lastPurchaseDate: purchase.createdAt,
                    averagePrice: 0
                };
            }
            
            inventory[key].totalBoxes += parseInt(purchase.boxCount) || 0;
            inventory[key].totalWeight += this.parseNumber(purchase.netWeight);
            inventory[key].totalValue += this.parseNumber(purchase.totalAmount);
            
            // تحديث تاريخ آخر شراء
            if (new Date(purchase.createdAt) > new Date(inventory[key].lastPurchaseDate)) {
                inventory[key].lastPurchaseDate = purchase.createdAt;
            }
        });
        
        // حساب متوسط السعر
        Object.values(inventory).forEach(item => {
            if (item.totalWeight > 0) {
                item.averagePrice = item.totalValue / item.totalWeight;
            }
            item.totalValue = this.roundNumber(item.totalValue);
            item.totalWeight = this.roundNumber(item.totalWeight);
            item.averagePrice = this.roundNumber(item.averagePrice);
        });
        
        return Object.values(inventory);
    }

    /**
     * حساب الأرباح والخسائر
     */
    calculateProfitLoss(startDate, endDate) {
        const purchases = storage.filterByDate('purchases', startDate, endDate);
        const invoices = storage.filterByDate('invoices', startDate, endDate);
        
        let totalRevenue = 0;
        let totalCosts = 0;
        let totalDeposits = 0;
        
        // حساب الإيرادات من المشتريات
        purchases.forEach(purchase => {
            totalRevenue += this.parseNumber(purchase.totalAmount);
            totalDeposits += this.parseNumber(purchase.deposit);
        });
        
        // حساب التكاليف من الفواتير
        invoices.forEach(invoice => {
            totalCosts += this.parseNumber(invoice.netAmount);
        });
        
        const grossProfit = totalRevenue - totalCosts;
        const netProfit = grossProfit + totalDeposits; // الرهون تُعتبر إيراد إضافي
        const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;
        
        return {
            totalRevenue: this.roundNumber(totalRevenue),
            totalCosts: this.roundNumber(totalCosts),
            totalDeposits: this.roundNumber(totalDeposits),
            grossProfit: this.roundNumber(grossProfit),
            netProfit: this.roundNumber(netProfit),
            profitMargin: this.roundNumber(profitMargin, 2)
        };
    }

    /**
     * حساب تقرير شامل للمورد
     */
    calculateSupplierReport(supplierName, startDate = null, endDate = null) {
        let purchases = storage.loadItems('purchases')
            .filter(p => p.supplierName === supplierName);
        
        if (startDate && endDate) {
            purchases = storage.filterByDate('purchases', startDate, endDate)
                .filter(p => p.supplierName === supplierName);
        }
        
        const report = {
            supplierName,
            totalPurchases: purchases.length,
            totalAmount: 0,
            totalWeight: 0,
            totalBoxes: 0,
            totalDeposits: 0,
            products: {},
            paymentStatus: {
                paid: 0,
                partial: 0,
                unpaid: 0
            },
            averagePrice: 0,
            firstPurchase: null,
            lastPurchase: null
        };
        
        purchases.forEach(purchase => {
            const amount = this.parseNumber(purchase.totalAmount);
            const weight = this.parseNumber(purchase.netWeight);
            const boxes = parseInt(purchase.boxCount) || 0;
            const deposit = this.parseNumber(purchase.deposit);
            
            report.totalAmount += amount;
            report.totalWeight += weight;
            report.totalBoxes += boxes;
            report.totalDeposits += deposit;
            
            // منتجات المورد
            const product = purchase.productType;
            if (!report.products[product]) {
                report.products[product] = {
                    name: product,
                    totalAmount: 0,
                    totalWeight: 0,
                    totalBoxes: 0,
                    count: 0,
                    averagePrice: 0
                };
            }
            
            report.products[product].totalAmount += amount;
            report.products[product].totalWeight += weight;
            report.products[product].totalBoxes += boxes;
            report.products[product].count += 1;
            
            // حالة الدفع
            const paymentStatus = purchase.paymentStatus || 'unpaid';
            if (report.paymentStatus[paymentStatus] !== undefined) {
                report.paymentStatus[paymentStatus] += amount;
            }
            
            // تواريخ أول وآخر شراء
            const purchaseDate = new Date(purchase.createdAt);
            if (!report.firstPurchase || purchaseDate < new Date(report.firstPurchase)) {
                report.firstPurchase = purchase.createdAt;
            }
            if (!report.lastPurchase || purchaseDate > new Date(report.lastPurchase)) {
                report.lastPurchase = purchase.createdAt;
            }
        });
        
        // حساب متوسط السعر
        if (report.totalWeight > 0) {
            report.averagePrice = report.totalAmount / report.totalWeight;
        }
        
        // حساب متوسط السعر لكل منتج
        Object.values(report.products).forEach(product => {
            if (product.totalWeight > 0) {
                product.averagePrice = product.totalAmount / product.totalWeight;
            }
            product.averagePrice = this.roundNumber(product.averagePrice);
        });
        
        // تحويل المنتجات إلى مصفوفة مرتبة
        report.products = Object.values(report.products)
            .sort((a, b) => b.totalAmount - a.totalAmount);
        
        // تقريب الأرقام
        report.totalAmount = this.roundNumber(report.totalAmount);
        report.totalWeight = this.roundNumber(report.totalWeight);
        report.totalDeposits = this.roundNumber(report.totalDeposits);
        report.averagePrice = this.roundNumber(report.averagePrice);
        
        Object.keys(report.paymentStatus).forEach(key => {
            report.paymentStatus[key] = this.roundNumber(report.paymentStatus[key]);
        });
        
        return report;
    }

    /**
     * حساب تقرير يومي شامل
     */
    calculateDailyReport(date = null) {
        const targetDate = date || Utils.getCurrentDate();
        const purchases = storage.filterByDate('purchases', targetDate, targetDate);
        const invoices = storage.filterByDate('invoices', targetDate, targetDate);
        
        const report = {
            date: targetDate,
            summary: {
                totalPurchases: purchases.length,
                totalInvoices: invoices.length,
                totalRevenue: 0,
                totalCosts: 0,
                totalDeposits: 0,
                netProfit: 0
            },
            purchases: this.calculateSalesStatistics(purchases),
            suppliers: {},
            products: {}
        };
        
        // تجميع بيانات الموردين والمنتجات
        purchases.forEach(purchase => {
            const supplier = purchase.supplierName;
            const product = purchase.productType;
            const amount = this.parseNumber(purchase.totalAmount);
            const weight = this.parseNumber(purchase.netWeight);
            const deposit = this.parseNumber(purchase.deposit);
            
            report.summary.totalRevenue += amount;
            report.summary.totalDeposits += deposit;
            
            // إحصائيات الموردين
            if (!report.suppliers[supplier]) {
                report.suppliers[supplier] = {
                    name: supplier,
                    totalAmount: 0,
                    totalWeight: 0,
                    purchaseCount: 0
                };
            }
            report.suppliers[supplier].totalAmount += amount;
            report.suppliers[supplier].totalWeight += weight;
            report.suppliers[supplier].purchaseCount += 1;
            
            // إحصائيات المنتجات
            if (!report.products[product]) {
                report.products[product] = {
                    name: product,
                    totalAmount: 0,
                    totalWeight: 0,
                    purchaseCount: 0
                };
            }
            report.products[product].totalAmount += amount;
            report.products[product].totalWeight += weight;
            report.products[product].purchaseCount += 1;
        });
        
        // حساب التكاليف من الفواتير
        invoices.forEach(invoice => {
            report.summary.totalCosts += this.parseNumber(invoice.netAmount);
        });
        
        // حساب صافي الربح
        report.summary.netProfit = report.summary.totalRevenue - report.summary.totalCosts + report.summary.totalDeposits;
        
        // تحويل الكائنات إلى مصفوفات مرتبة
        report.suppliers = Object.values(report.suppliers)
            .sort((a, b) => b.totalAmount - a.totalAmount);
        
        report.products = Object.values(report.products)
            .sort((a, b) => b.totalAmount - a.totalAmount);
        
        // تقريب الأرقام
        Object.keys(report.summary).forEach(key => {
            if (typeof report.summary[key] === 'number') {
                report.summary[key] = this.roundNumber(report.summary[key]);
            }
        });
        
        return report;
    }

    /**
     * وظائف مساعدة
     */
    parseNumber(value) {
        const number = parseFloat(value);
        return isNaN(number) ? 0 : number;
    }

    roundNumber(number, decimals = 3) {
        return Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals);
    }

    getBoxTypeById(id) {
        const boxTypes = storage.loadItems('boxTypes');
        return boxTypes.find(box => box.id == id);
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
}

// إنشاء مثيل عام لمحرك الحسابات
const calculator = new CalculationEngine();
