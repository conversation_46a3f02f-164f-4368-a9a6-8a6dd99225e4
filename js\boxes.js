/**
 * إدارة أنواع الصناديق
 * يحتوي على جميع وظائف إدارة أنواع الصناديق وأوزانها وأسعار الحمولة
 */

class BoxesManager {
    constructor() {
        this.boxTypes = [];
        this.init();
    }

    /**
     * تهيئة مدير الصناديق
     */
    init() {
        this.loadBoxTypes();
        this.bindEvents();
        this.renderBoxesTable();
    }

    /**
     * تحميل أنواع الصناديق من التخزين
     */
    loadBoxTypes() {
        this.boxTypes = storage.loadItems('boxTypes');
        // إذا لم توجد أنواع صناديق، استخدام الافتراضية
        if (this.boxTypes.length === 0) {
            this.boxTypes = storage.getDefaultBoxTypes();
            this.boxTypes.forEach(boxType => {
                storage.saveItem('boxTypes', boxType);
            });
        }
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر إضافة نوع صندوق جديد
        const addBoxTypeBtn = document.getElementById('add-box-type-btn');
        if (addBoxTypeBtn) {
            addBoxTypeBtn.addEventListener('click', () => this.showAddBoxTypeModal());
        }
    }

    /**
     * عرض نموذج إضافة نوع صندوق
     */
    showAddBoxTypeModal() {
        const name = prompt('اسم نوع الصندوق:');
        if (!name) return;

        const emptyWeight = prompt('الوزن الفارغ (كغ):', '0');
        if (emptyWeight === null) return;

        const loadingPrice = prompt('سعر الحمولة (د.ت):', '0');
        if (loadingPrice === null) return;

        const notes = prompt('ملاحظات (اختياري):', '') || '';

        this.addBoxType({
            name: Utils.cleanText(name),
            emptyWeight: parseFloat(emptyWeight) || 0,
            loadingPrice: parseFloat(loadingPrice) || 0,
            notes: Utils.cleanText(notes)
        });
    }

    /**
     * إضافة نوع صندوق جديد
     */
    addBoxType(boxTypeData) {
        // التحقق من عدم تكرار الاسم
        const existingBoxType = this.boxTypes.find(b => 
            b.name.toLowerCase() === boxTypeData.name.toLowerCase()
        );
        
        if (existingBoxType) {
            Utils.showError('يوجد نوع صندوق بنفس الاسم مسبقاً');
            return;
        }

        const boxType = {
            ...boxTypeData,
            id: storage.generateId(this.boxTypes),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('boxTypes', boxType)) {
            this.loadBoxTypes();
            this.renderBoxesTable();
            Utils.showSuccess('تم إضافة نوع الصندوق بنجاح');
        } else {
            Utils.showError('فشل في إضافة نوع الصندوق');
        }
    }

    /**
     * تحرير نوع صندوق
     */
    editBoxType(boxTypeId) {
        const boxType = this.boxTypes.find(b => b.id == boxTypeId);
        if (!boxType) return;

        const name = prompt('اسم نوع الصندوق:', boxType.name);
        if (!name) return;

        const emptyWeight = prompt('الوزن الفارغ (كغ):', boxType.emptyWeight.toString());
        if (emptyWeight === null) return;

        const loadingPrice = prompt('سعر الحمولة (د.ت):', boxType.loadingPrice.toString());
        if (loadingPrice === null) return;

        const notes = prompt('ملاحظات:', boxType.notes || '');
        if (notes === null) return;

        const updatedBoxType = {
            ...boxType,
            name: Utils.cleanText(name),
            emptyWeight: parseFloat(emptyWeight) || 0,
            loadingPrice: parseFloat(loadingPrice) || 0,
            notes: Utils.cleanText(notes),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('boxTypes', updatedBoxType)) {
            this.loadBoxTypes();
            this.renderBoxesTable();
            Utils.showSuccess('تم تحديث نوع الصندوق بنجاح');
        } else {
            Utils.showError('فشل في تحديث نوع الصندوق');
        }
    }

    /**
     * حذف نوع صندوق
     */
    deleteBoxType(boxTypeId) {
        const boxType = this.boxTypes.find(b => b.id == boxTypeId);
        if (!boxType) return;

        // التحقق من وجود مشتريات مرتبطة بنوع الصندوق
        const relatedPurchases = storage.loadItems('purchases')
            .filter(p => p.boxType === boxType.name);

        if (relatedPurchases.length > 0) {
            Utils.showError(`لا يمكن حذف نوع الصندوق "${boxType.name}" لأنه مرتبط بـ ${relatedPurchases.length} عملية شراء`);
            return;
        }

        Utils.showConfirmation(
            `هل أنت متأكد من حذف نوع الصندوق "${boxType.name}"؟`,
            () => {
                if (storage.deleteItem('boxTypes', boxTypeId)) {
                    this.loadBoxTypes();
                    this.renderBoxesTable();
                    Utils.showSuccess('تم حذف نوع الصندوق بنجاح');
                } else {
                    Utils.showError('فشل في حذف نوع الصندوق');
                }
            }
        );
    }

    /**
     * عرض جدول أنواع الصناديق
     */
    renderBoxesTable() {
        const tbody = document.getElementById('boxes-tbody');
        if (!tbody) return;

        if (this.boxTypes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">لا توجد أنواع صناديق مسجلة</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.boxTypes.map(boxType => {
            const usage = this.calculateBoxTypeUsage(boxType.name);
            
            return `
                <tr>
                    <td>
                        <div class="box-type-info">
                            <strong>${boxType.name}</strong>
                            <small class="text-muted d-block">
                                استُخدم ${usage.totalUsage} مرة
                            </small>
                        </div>
                    </td>
                    <td class="text-center">
                        <strong>${Utils.formatNumber(boxType.emptyWeight)} كغ</strong>
                    </td>
                    <td class="text-center">
                        <strong class="text-success">${Utils.formatCurrency(boxType.loadingPrice)}</strong>
                    </td>
                    <td>
                        <div class="box-notes">
                            ${boxType.notes || '<span class="text-muted">لا توجد ملاحظات</span>'}
                            ${boxType.name === 'بلا حمولة' ? 
                                '<br><small class="text-info"><i class="fas fa-info-circle"></i> الوزن الصافي × 10</small>' : ''}
                            ${boxType.name === 'Carton' ? 
                                '<br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> يُدخل وزنه يدوياً</small>' : ''}
                        </div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view" onclick="boxesManager.viewBoxTypeDetails('${boxType.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit" onclick="boxesManager.editBoxType('${boxType.id}')" title="تحرير">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${!this.isDefaultBoxType(boxType.name) ? `
                                <button class="action-btn delete" onclick="boxesManager.deleteBoxType('${boxType.id}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * التحقق من كون نوع الصندوق افتراضي
     */
    isDefaultBoxType(name) {
        const defaultNames = [
            'الصندوق الكبير', 'Plato', 'Lam plus', '4 Carro', 
            'Scarface', 'Lam demi', 'Lam mini', 'Carton', 'بلا حمولة'
        ];
        return defaultNames.includes(name);
    }

    /**
     * حساب استخدام نوع الصندوق
     */
    calculateBoxTypeUsage(boxTypeName) {
        const purchases = storage.loadItems('purchases')
            .filter(p => p.boxType === boxTypeName);

        let totalUsage = 0;
        let totalBoxes = 0;
        let totalWeight = 0;
        let totalDeposits = 0;

        purchases.forEach(purchase => {
            totalUsage += 1;
            totalBoxes += parseInt(purchase.boxCount) || 0;
            totalWeight += parseFloat(purchase.netWeight) || 0;
            totalDeposits += parseFloat(purchase.deposit) || 0;
        });

        return {
            totalUsage,
            totalBoxes,
            totalWeight,
            totalDeposits,
            averageBoxesPerUse: totalUsage > 0 ? totalBoxes / totalUsage : 0
        };
    }

    /**
     * عرض تفاصيل نوع الصندوق
     */
    viewBoxTypeDetails(boxTypeId) {
        const boxType = this.boxTypes.find(b => b.id == boxTypeId);
        if (!boxType) return;

        const usage = this.calculateBoxTypeUsage(boxType.name);
        const purchases = storage.loadItems('purchases')
            .filter(p => p.boxType === boxType.name)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تجميع الاستخدام حسب المورد
        const supplierUsage = {};
        purchases.forEach(purchase => {
            const supplier = purchase.supplierName;
            if (!supplierUsage[supplier]) {
                supplierUsage[supplier] = {
                    name: supplier,
                    totalBoxes: 0,
                    totalWeight: 0,
                    totalDeposits: 0,
                    usageCount: 0
                };
            }
            supplierUsage[supplier].totalBoxes += parseInt(purchase.boxCount) || 0;
            supplierUsage[supplier].totalWeight += parseFloat(purchase.netWeight) || 0;
            supplierUsage[supplier].totalDeposits += parseFloat(purchase.deposit) || 0;
            supplierUsage[supplier].usageCount += 1;
        });

        const sortedSuppliers = Object.values(supplierUsage)
            .sort((a, b) => b.totalBoxes - a.totalBoxes);

        // تجميع الاستخدام حسب المنتج
        const productUsage = {};
        purchases.forEach(purchase => {
            const product = purchase.productType;
            if (!productUsage[product]) {
                productUsage[product] = {
                    name: product,
                    totalBoxes: 0,
                    totalWeight: 0,
                    usageCount: 0
                };
            }
            productUsage[product].totalBoxes += parseInt(purchase.boxCount) || 0;
            productUsage[product].totalWeight += parseFloat(purchase.netWeight) || 0;
            productUsage[product].usageCount += 1;
        });

        const sortedProducts = Object.values(productUsage)
            .sort((a, b) => b.totalBoxes - a.totalBoxes);

        const detailsHTML = `
            <div class="box-type-details">
                <div class="box-type-header">
                    <h2><i class="fas fa-box"></i> ${boxType.name}</h2>
                    <div class="box-type-specs">
                        <div class="spec-item">
                            <strong>الوزن الفارغ:</strong> ${Utils.formatNumber(boxType.emptyWeight)} كغ
                        </div>
                        <div class="spec-item">
                            <strong>سعر الحمولة:</strong> ${Utils.formatCurrency(boxType.loadingPrice)}
                        </div>
                        <div class="spec-item">
                            <strong>الملاحظات:</strong> ${boxType.notes || 'لا توجد ملاحظات'}
                        </div>
                        <div class="spec-item">
                            <strong>تاريخ الإضافة:</strong> ${Utils.formatDateTime(boxType.createdAt)}
                        </div>
                    </div>
                </div>

                <div class="usage-summary">
                    <div class="summary-card">
                        <h4>إجمالي الاستخدام</h4>
                        <span class="summary-value">${usage.totalUsage} مرة</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الصناديق</h4>
                        <span class="summary-value">${usage.totalBoxes}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الوزن</h4>
                        <span class="summary-value">${Utils.formatNumber(usage.totalWeight)} كغ</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الرهون</h4>
                        <span class="summary-value">${Utils.formatCurrency(usage.totalDeposits)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط الصناديق/استخدام</h4>
                        <span class="summary-value">${Utils.formatNumber(usage.averageBoxesPerUse)}</span>
                    </div>
                </div>

                <div class="supplier-usage">
                    <h3>الاستخدام حسب المورد</h3>
                    ${sortedSuppliers.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المورد</th>
                                    <th>عدد المرات</th>
                                    <th>إجمالي الصناديق</th>
                                    <th>إجمالي الوزن</th>
                                    <th>إجمالي الرهون</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${sortedSuppliers.map(supplier => `
                                    <tr>
                                        <td><strong>${supplier.name}</strong></td>
                                        <td>${supplier.usageCount}</td>
                                        <td>${supplier.totalBoxes}</td>
                                        <td>${Utils.formatNumber(supplier.totalWeight)} كغ</td>
                                        <td>${Utils.formatCurrency(supplier.totalDeposits)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-center">لا يوجد استخدام</p>'}
                </div>

                <div class="product-usage">
                    <h3>الاستخدام حسب المنتج</h3>
                    ${sortedProducts.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>عدد المرات</th>
                                    <th>إجمالي الصناديق</th>
                                    <th>إجمالي الوزن</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${sortedProducts.map(product => `
                                    <tr>
                                        <td><strong>${product.name}</strong></td>
                                        <td>${product.usageCount}</td>
                                        <td>${product.totalBoxes}</td>
                                        <td>${Utils.formatNumber(product.totalWeight)} كغ</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-center">لا يوجد استخدام</p>'}
                </div>

                <div class="recent-usage">
                    <h3>آخر 10 استخدامات</h3>
                    ${purchases.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المنتج</th>
                                    <th>عدد الصناديق</th>
                                    <th>الوزن الصافي</th>
                                    <th>الرهن</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchases.slice(0, 10).map(purchase => `
                                    <tr>
                                        <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                        <td>${purchase.supplierName}</td>
                                        <td>${purchase.productType}</td>
                                        <td>${purchase.boxCount || 0}</td>
                                        <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                                        <td>${Utils.formatCurrency(purchase.deposit)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-center">لا يوجد استخدام</p>'}
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل نوع الصندوق - ${boxType.name}`);
    }

    /**
     * الحصول على قائمة أنواع الصناديق للاختيار
     */
    getBoxTypesForSelect() {
        return this.boxTypes.map(boxType => ({
            id: boxType.id,
            name: boxType.name,
            emptyWeight: boxType.emptyWeight,
            loadingPrice: boxType.loadingPrice,
            notes: boxType.notes
        }));
    }

    /**
     * البحث عن نوع صندوق بالاسم
     */
    findBoxTypeByName(name) {
        return this.boxTypes.find(b => b.name.toLowerCase() === name.toLowerCase());
    }

    /**
     * البحث عن نوع صندوق بالمعرف
     */
    findBoxTypeById(id) {
        return this.boxTypes.find(b => b.id == id);
    }

    /**
     * حساب الرهن لنوع صندوق معين
     */
    calculateDepositForBoxType(boxTypeName, boxCount, netWeight = 0) {
        const boxType = this.findBoxTypeByName(boxTypeName);
        if (!boxType) return 0;

        return calculator.calculateDeposit(boxType, boxCount, netWeight);
    }

    /**
     * تصدير أنواع الصناديق إلى CSV
     */
    exportBoxTypes() {
        const boxTypesData = this.boxTypes.map(boxType => {
            const usage = this.calculateBoxTypeUsage(boxType.name);
            return {
                'الرقم': boxType.id,
                'الاسم': boxType.name,
                'الوزن الفارغ (كغ)': boxType.emptyWeight,
                'سعر الحمولة (د.ت)': boxType.loadingPrice,
                'الملاحظات': boxType.notes || '',
                'تاريخ الإضافة': Utils.formatDate(boxType.createdAt),
                'إجمالي الاستخدام': usage.totalUsage,
                'إجمالي الصناديق': usage.totalBoxes,
                'إجمالي الوزن (كغ)': usage.totalWeight,
                'إجمالي الرهون (د.ت)': usage.totalDeposits
            };
        });

        Utils.exportToCSV(boxTypesData, `انواع_الصناديق_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * تحديث عرض الصناديق
     */
    updateBoxesDisplay() {
        this.renderBoxesTable();
    }
}

// إنشاء مثيل عام لمدير الصناديق
const boxesManager = new BoxesManager();
