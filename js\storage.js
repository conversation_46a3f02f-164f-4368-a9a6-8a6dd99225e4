/**
 * نظام إدارة التخزين المحلي للبيانات
 * يدعم حفظ البيانات بشكل دائم مع نظام تاريخ دقيق
 */

class StorageManager {
    constructor() {
        this.storageKey = 'wholesale_market_data';
        this.initializeStorage();
    }

    /**
     * تهيئة التخزين مع البيانات الافتراضية
     */
    initializeStorage() {
        if (!localStorage.getItem(this.storageKey)) {
            const defaultData = {
                customers: [],
                suppliers: [],
                products: [],
                boxTypes: this.getDefaultBoxTypes(),
                purchases: [],
                invoices: [],
                settings: this.getDefaultSettings(),
                lastBackup: null,
                version: '1.0.0'
            };
            this.saveData(defaultData);
        }
    }

    /**
     * الحصول على أنواع الصناديق الافتراضية
     */
    getDefaultBoxTypes() {
        return [
            {
                id: 1,
                name: 'الصندوق الكبير',
                emptyWeight: 2.0,
                loadingPrice: 0.200,
                notes: 'صندوق كبير للخضروات'
            },
            {
                id: 2,
                name: 'Plato',
                emptyWeight: 1.5,
                loadingPrice: 0.200,
                notes: 'صندوق متوسط'
            },
            {
                id: 3,
                name: 'Lam plus',
                emptyWeight: 0.75,
                loadingPrice: 0.170,
                notes: 'صندوق صغير'
            },
            {
                id: 4,
                name: '4 Carro',
                emptyWeight: 0.75,
                loadingPrice: 0.170,
                notes: 'صندوق صغير'
            },
            {
                id: 5,
                name: 'Scarface',
                emptyWeight: 0.75,
                loadingPrice: 0.170,
                notes: 'صندوق صغير'
            },
            {
                id: 6,
                name: 'Lam demi',
                emptyWeight: 0.7,
                loadingPrice: 0.170,
                notes: 'صندوق صغير'
            },
            {
                id: 7,
                name: 'Lam mini',
                emptyWeight: 0.6,
                loadingPrice: 0.170,
                notes: 'صندوق صغير جداً'
            },
            {
                id: 8,
                name: 'Carton',
                emptyWeight: 0,
                loadingPrice: 0.300,
                notes: 'يُدخل وزنه يدوياً'
            },
            {
                id: 9,
                name: 'بلا حمولة',
                emptyWeight: 0,
                loadingPrice: 0,
                notes: 'الوزن الصافي × 10'
            }
        ];
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    getDefaultSettings() {
        return {
            marketName: 'سوق الجملة للخضر والغلال - جرزونة',
            posNumber: '14',
            ownerName: 'عصام سولي',
            currency: 'د.ت',
            taxRate: 19,
            fourPercent: 4,
            sevenPercent: 7,
            language: 'ar',
            theme: 'default'
        };
    }

    /**
     * حفظ جميع البيانات
     */
    saveData(data) {
        try {
            data.lastModified = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    /**
     * تحميل جميع البيانات
     */
    loadData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    }

    /**
     * حفظ عنصر محدد
     */
    saveItem(category, item) {
        const data = this.loadData();
        if (!data) return false;

        if (!data[category]) {
            data[category] = [];
        }

        // إضافة معرف فريد إذا لم يكن موجوداً
        if (!item.id) {
            item.id = this.generateId(data[category]);
        }

        // إضافة طابع زمني
        item.createdAt = item.createdAt || new Date().toISOString();
        item.updatedAt = new Date().toISOString();

        // البحث عن العنصر الموجود وتحديثه أو إضافة عنصر جديد
        const existingIndex = data[category].findIndex(existing => existing.id === item.id);
        if (existingIndex !== -1) {
            data[category][existingIndex] = { ...data[category][existingIndex], ...item };
        } else {
            data[category].push(item);
        }

        return this.saveData(data);
    }

    /**
     * تحميل عناصر من فئة محددة
     */
    loadItems(category) {
        const data = this.loadData();
        return data && data[category] ? data[category] : [];
    }

    /**
     * حذف عنصر
     */
    deleteItem(category, id) {
        const data = this.loadData();
        if (!data || !data[category]) return false;

        data[category] = data[category].filter(item => item.id !== id);
        return this.saveData(data);
    }

    /**
     * البحث في العناصر
     */
    searchItems(category, searchTerm, fields = []) {
        const items = this.loadItems(category);
        if (!searchTerm) return items;

        const term = searchTerm.toLowerCase();
        return items.filter(item => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(item).some(value => 
                    value && value.toString().toLowerCase().includes(term)
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field => 
                    item[field] && item[field].toString().toLowerCase().includes(term)
                );
            }
        });
    }

    /**
     * تصفية العناصر حسب التاريخ
     */
    filterByDate(category, startDate, endDate, dateField = 'createdAt') {
        const items = this.loadItems(category);
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // نهاية اليوم

        return items.filter(item => {
            const itemDate = new Date(item[dateField]);
            return itemDate >= start && itemDate <= end;
        });
    }

    /**
     * الحصول على عناصر اليوم
     */
    getTodayItems(category, dateField = 'createdAt') {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

        const items = this.loadItems(category);
        return items.filter(item => {
            const itemDate = new Date(item[dateField]);
            return itemDate >= startOfDay && itemDate <= endOfDay;
        });
    }

    /**
     * الحصول على عناصر الأمس
     */
    getYesterdayItems(category, dateField = 'createdAt') {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        const endOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999);

        const items = this.loadItems(category);
        return items.filter(item => {
            const itemDate = new Date(item[dateField]);
            return itemDate >= startOfDay && itemDate <= endOfDay;
        });
    }

    /**
     * توليد معرف فريد
     */
    generateId(existingItems = []) {
        const maxId = existingItems.reduce((max, item) => 
            Math.max(max, parseInt(item.id) || 0), 0
        );
        return (maxId + 1).toString();
    }

    /**
     * إنشاء نسخة احتياطية
     */
    createBackup() {
        const data = this.loadData();
        if (!data) return null;

        const backup = {
            ...data,
            backupDate: new Date().toISOString(),
            version: data.version || '1.0.0'
        };

        // تحديث تاريخ آخر نسخة احتياطية
        data.lastBackup = backup.backupDate;
        this.saveData(data);

        return backup;
    }

    /**
     * استعادة من نسخة احتياطية
     */
    restoreFromBackup(backupData) {
        try {
            if (!backupData || typeof backupData !== 'object') {
                throw new Error('بيانات النسخة الاحتياطية غير صحيحة');
            }

            // التحقق من وجود البيانات الأساسية
            const requiredFields = ['customers', 'suppliers', 'products', 'boxTypes', 'purchases', 'invoices', 'settings'];
            for (const field of requiredFields) {
                if (!backupData[field]) {
                    backupData[field] = [];
                }
            }

            // إضافة طابع زمني للاستعادة
            backupData.restoredAt = new Date().toISOString();
            
            return this.saveData(backupData);
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        try {
            localStorage.removeItem(this.storageKey);
            this.initializeStorage();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }

    /**
     * الحصول على إحصائيات البيانات
     */
    getDataStatistics() {
        const data = this.loadData();
        if (!data) return null;

        return {
            customers: data.customers ? data.customers.length : 0,
            suppliers: data.suppliers ? data.suppliers.length : 0,
            products: data.products ? data.products.length : 0,
            purchases: data.purchases ? data.purchases.length : 0,
            invoices: data.invoices ? data.invoices.length : 0,
            todayPurchases: this.getTodayItems('purchases').length,
            todayInvoices: this.getTodayItems('invoices').length,
            lastModified: data.lastModified,
            lastBackup: data.lastBackup
        };
    }

    /**
     * حساب إجمالي المبيعات
     */
    calculateTotalSales(startDate = null, endDate = null) {
        let purchases = this.loadItems('purchases');
        
        if (startDate && endDate) {
            purchases = this.filterByDate('purchases', startDate, endDate);
        }

        return purchases.reduce((total, purchase) => {
            return total + (parseFloat(purchase.totalAmount) || 0);
        }, 0);
    }

    /**
     * حساب مبيعات اليوم
     */
    getTodaySales() {
        const todayPurchases = this.getTodayItems('purchases');
        return todayPurchases.reduce((total, purchase) => {
            return total + (parseFloat(purchase.totalAmount) || 0);
        }, 0);
    }

    /**
     * حساب مبيعات الأمس
     */
    getYesterdaySales() {
        const yesterdayPurchases = this.getYesterdayItems('purchases');
        return yesterdayPurchases.reduce((total, purchase) => {
            return total + (parseFloat(purchase.totalAmount) || 0);
        }, 0);
    }

    /**
     * الحصول على أفضل العملاء
     */
    getTopCustomers(limit = 5) {
        const purchases = this.loadItems('purchases');
        const customerSales = {};

        purchases.forEach(purchase => {
            const customerId = purchase.customerId || purchase.supplierName;
            if (!customerSales[customerId]) {
                customerSales[customerId] = {
                    name: purchase.supplierName || 'غير محدد',
                    totalSales: 0,
                    purchaseCount: 0
                };
            }
            customerSales[customerId].totalSales += parseFloat(purchase.totalAmount) || 0;
            customerSales[customerId].purchaseCount += 1;
        });

        return Object.values(customerSales)
            .sort((a, b) => b.totalSales - a.totalSales)
            .slice(0, limit);
    }

    /**
     * الحصول على النشاطات الأخيرة
     */
    getRecentActivities(limit = 10) {
        const activities = [];
        
        // إضافة المشتريات الأخيرة
        const recentPurchases = this.loadItems('purchases')
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);
        
        recentPurchases.forEach(purchase => {
            activities.push({
                type: 'purchase',
                title: `مشترى جديد: ${purchase.productType}`,
                description: `من ${purchase.supplierName} - ${purchase.totalAmount} د.ت`,
                time: purchase.createdAt,
                icon: 'fas fa-shopping-cart'
            });
        });

        // إضافة الفواتير الأخيرة
        const recentInvoices = this.loadItems('invoices')
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 3);
        
        recentInvoices.forEach(invoice => {
            activities.push({
                type: 'invoice',
                title: `فاتورة جديدة: ${invoice.supplierName}`,
                description: `المبلغ الصافي: ${invoice.netAmount} د.ت`,
                time: invoice.createdAt,
                icon: 'fas fa-file-invoice'
            });
        });

        // إضافة العملاء الجدد
        const recentCustomers = this.loadItems('customers')
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 2);
        
        recentCustomers.forEach(customer => {
            activities.push({
                type: 'customer',
                title: `عميل جديد: ${customer.name}`,
                description: `تم إضافة عميل جديد`,
                time: customer.createdAt,
                icon: 'fas fa-user-plus'
            });
        });

        return activities
            .sort((a, b) => new Date(b.time) - new Date(a.time))
            .slice(0, limit);
    }
}

// إنشاء مثيل عام لمدير التخزين
const storage = new StorageManager();
