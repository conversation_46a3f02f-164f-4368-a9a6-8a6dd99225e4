/**
 * إدارة الموردين
 * يحتوي على جميع وظائف إدارة الموردين وحساب إحصائياتهم
 */

class SuppliersManager {
    constructor() {
        this.suppliers = [];
        this.init();
    }

    /**
     * تهيئة مدير الموردين
     */
    init() {
        this.loadSuppliers();
        this.bindEvents();
        this.renderSuppliersTable();
    }

    /**
     * تحميل الموردين من التخزين
     */
    loadSuppliers() {
        this.suppliers = storage.loadItems('suppliers');
        // إذا لم توجد موردين، إنشاء قائمة من المشتريات الموجودة
        if (this.suppliers.length === 0) {
            this.generateSuppliersFromPurchases();
        }
    }

    /**
     * إنشاء قائمة الموردين من المشتريات الموجودة
     */
    generateSuppliersFromPurchases() {
        const purchases = storage.loadItems('purchases');
        const supplierNames = [...new Set(purchases.map(p => p.supplierName).filter(name => name))];
        
        supplierNames.forEach(name => {
            const supplier = {
                id: storage.generateId(this.suppliers),
                name: name,
                phone: '',
                address: '',
                createdAt: Utils.getCurrentDateTime(),
                updatedAt: Utils.getCurrentDateTime()
            };
            this.suppliers.push(supplier);
            storage.saveItem('suppliers', supplier);
        });
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر إضافة مورد جديد
        const addSupplierBtn = document.getElementById('add-supplier-btn');
        if (addSupplierBtn) {
            addSupplierBtn.addEventListener('click', () => this.showSupplierModal());
        }
    }

    /**
     * عرض نموذج المورد
     */
    showSupplierModal(supplierId = null) {
        // يمكن تطوير هذه الوظيفة لاحقاً لإضافة نموذج مورد مخصص
        const name = prompt('اسم المورد:');
        if (!name) return;

        const phone = prompt('رقم الهاتف (اختياري):') || '';
        const address = prompt('العنوان (اختياري):') || '';

        const supplierData = {
            name: Utils.cleanText(name),
            phone: Utils.cleanText(phone),
            address: Utils.cleanText(address)
        };

        if (supplierId) {
            this.updateSupplier(supplierId, supplierData);
        } else {
            this.addSupplier(supplierData);
        }
    }

    /**
     * إضافة مورد جديد
     */
    addSupplier(supplierData) {
        // التحقق من عدم تكرار الاسم
        const existingSupplier = this.suppliers.find(s => 
            s.name.toLowerCase() === supplierData.name.toLowerCase()
        );
        
        if (existingSupplier) {
            Utils.showError('يوجد مورد بنفس الاسم مسبقاً');
            return;
        }

        const supplier = {
            ...supplierData,
            id: storage.generateId(this.suppliers),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('suppliers', supplier)) {
            this.loadSuppliers();
            this.renderSuppliersTable();
            Utils.showSuccess('تم إضافة المورد بنجاح');
        } else {
            Utils.showError('فشل في إضافة المورد');
        }
    }

    /**
     * تحديث مورد موجود
     */
    updateSupplier(supplierId, supplierData) {
        const supplier = {
            ...supplierData,
            id: supplierId,
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('suppliers', supplier)) {
            this.loadSuppliers();
            this.renderSuppliersTable();
            Utils.showSuccess('تم تحديث بيانات المورد بنجاح');
        } else {
            Utils.showError('فشل في تحديث بيانات المورد');
        }
    }

    /**
     * حذف مورد
     */
    deleteSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        // التحقق من وجود مشتريات مرتبطة بالمورد
        const relatedPurchases = storage.loadItems('purchases')
            .filter(p => p.supplierName === supplier.name);

        if (relatedPurchases.length > 0) {
            Utils.showError(`لا يمكن حذف المورد "${supplier.name}" لأنه مرتبط بـ ${relatedPurchases.length} عملية شراء`);
            return;
        }

        Utils.showConfirmation(
            `هل أنت متأكد من حذف المورد "${supplier.name}"؟`,
            () => {
                if (storage.deleteItem('suppliers', supplierId)) {
                    this.loadSuppliers();
                    this.renderSuppliersTable();
                    Utils.showSuccess('تم حذف المورد بنجاح');
                } else {
                    Utils.showError('فشل في حذف المورد');
                }
            }
        );
    }

    /**
     * عرض جدول الموردين
     */
    renderSuppliersTable() {
        const tbody = document.getElementById('suppliers-tbody');
        if (!tbody) return;

        // حساب إحصائيات كل مورد
        const suppliersWithStats = this.suppliers.map(supplier => {
            const stats = this.calculateSupplierStats(supplier.name);
            return { ...supplier, ...stats };
        });

        // ترتيب الموردين حسب إجمالي المبيعات
        suppliersWithStats.sort((a, b) => b.totalSales - a.totalSales);

        if (suppliersWithStats.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">لا توجد موردين مسجلين</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = suppliersWithStats.map(supplier => `
            <tr>
                <td>
                    <div class="supplier-name">
                        <strong>${supplier.name}</strong>
                        ${supplier.phone ? `
                            <small class="text-muted d-block">
                                <i class="fas fa-phone"></i> ${supplier.phone}
                            </small>
                        ` : ''}
                    </div>
                </td>
                <td class="text-center">
                    <span class="badge bg-primary">${supplier.totalBoxes}</span>
                </td>
                <td>
                    <div class="products-list">
                        ${supplier.products.slice(0, 3).map(product => `
                            <span class="product-tag">${product}</span>
                        `).join('')}
                        ${supplier.products.length > 3 ? `
                            <span class="more-products">+${supplier.products.length - 3} أخرى</span>
                        ` : ''}
                    </div>
                </td>
                <td class="text-center">
                    <strong>${Utils.formatNumber(supplier.totalWeight)} كغ</strong>
                </td>
                <td class="text-center">
                    <strong class="text-success">${Utils.formatCurrency(supplier.averagePrice)}</strong>
                </td>
                <td class="text-center">
                    <strong class="text-primary">${Utils.formatCurrency(supplier.totalSales)}</strong>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="suppliersManager.viewSupplierDetails('${supplier.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="suppliersManager.showSupplierModal('${supplier.id}')" title="تحرير">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="suppliersManager.deleteSupplier('${supplier.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * حساب إحصائيات المورد
     */
    calculateSupplierStats(supplierName) {
        const purchases = storage.loadItems('purchases')
            .filter(p => p.supplierName === supplierName);

        let totalSales = 0;
        let totalWeight = 0;
        let totalBoxes = 0;
        const products = new Set();

        purchases.forEach(purchase => {
            totalSales += parseFloat(purchase.totalAmount) || 0;
            totalWeight += parseFloat(purchase.netWeight) || 0;
            totalBoxes += parseInt(purchase.boxCount) || 0;
            if (purchase.productType) {
                products.add(purchase.productType);
            }
        });

        const averagePrice = totalWeight > 0 ? totalSales / totalWeight : 0;

        return {
            totalSales,
            totalWeight,
            totalBoxes,
            averagePrice,
            products: Array.from(products),
            purchaseCount: purchases.length,
            lastPurchaseDate: purchases.length > 0 ? 
                Math.max(...purchases.map(p => new Date(p.createdAt).getTime())) : null
        };
    }

    /**
     * عرض تفاصيل المورد
     */
    viewSupplierDetails(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const stats = this.calculateSupplierStats(supplier.name);
        const purchases = storage.loadItems('purchases')
            .filter(p => p.supplierName === supplier.name)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // حساب إحصائيات المنتجات
        const productStats = {};
        purchases.forEach(purchase => {
            const product = purchase.productType;
            if (!productStats[product]) {
                productStats[product] = {
                    name: product,
                    totalAmount: 0,
                    totalWeight: 0,
                    totalBoxes: 0,
                    count: 0,
                    averagePrice: 0
                };
            }
            productStats[product].totalAmount += parseFloat(purchase.totalAmount) || 0;
            productStats[product].totalWeight += parseFloat(purchase.netWeight) || 0;
            productStats[product].totalBoxes += parseInt(purchase.boxCount) || 0;
            productStats[product].count += 1;
        });

        // حساب متوسط السعر لكل منتج
        Object.values(productStats).forEach(product => {
            if (product.totalWeight > 0) {
                product.averagePrice = product.totalAmount / product.totalWeight;
            }
        });

        const sortedProducts = Object.values(productStats)
            .sort((a, b) => b.totalAmount - a.totalAmount);

        const detailsHTML = `
            <div class="supplier-details">
                <div class="supplier-header">
                    <h2><i class="fas fa-truck"></i> ${supplier.name}</h2>
                    <div class="supplier-info">
                        <p><strong>الهاتف:</strong> ${supplier.phone || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${supplier.address || 'غير محدد'}</p>
                        <p><strong>تاريخ التسجيل:</strong> ${Utils.formatDateTime(supplier.createdAt)}</p>
                        <p><strong>آخر شراء:</strong> ${stats.lastPurchaseDate ? 
                            Utils.formatDateTime(new Date(stats.lastPurchaseDate).toISOString()) : 'لا يوجد'}</p>
                    </div>
                </div>

                <div class="supplier-stats">
                    <div class="stat-card">
                        <h4>إجمالي المشتريات</h4>
                        <span class="stat-value">${stats.purchaseCount}</span>
                    </div>
                    <div class="stat-card">
                        <h4>إجمالي المبيعات</h4>
                        <span class="stat-value">${Utils.formatCurrency(stats.totalSales)}</span>
                    </div>
                    <div class="stat-card">
                        <h4>إجمالي الوزن</h4>
                        <span class="stat-value">${Utils.formatNumber(stats.totalWeight)} كغ</span>
                    </div>
                    <div class="stat-card">
                        <h4>متوسط السعر</h4>
                        <span class="stat-value">${Utils.formatCurrency(stats.averagePrice)}</span>
                    </div>
                    <div class="stat-card">
                        <h4>إجمالي الصناديق</h4>
                        <span class="stat-value">${stats.totalBoxes}</span>
                    </div>
                    <div class="stat-card">
                        <h4>أنواع المنتجات</h4>
                        <span class="stat-value">${stats.products.length}</span>
                    </div>
                </div>

                <div class="products-breakdown">
                    <h3>تفصيل المنتجات</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>عدد المرات</th>
                                <th>إجمالي الوزن</th>
                                <th>إجمالي الصناديق</th>
                                <th>إجمالي المبلغ</th>
                                <th>متوسط السعر</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedProducts.map(product => `
                                <tr>
                                    <td><strong>${product.name}</strong></td>
                                    <td>${product.count}</td>
                                    <td>${Utils.formatNumber(product.totalWeight)} كغ</td>
                                    <td>${product.totalBoxes}</td>
                                    <td>${Utils.formatCurrency(product.totalAmount)}</td>
                                    <td>${Utils.formatCurrency(product.averagePrice)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="recent-purchases">
                    <h3>المشتريات الأخيرة</h3>
                    ${purchases.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>الوزن الصافي</th>
                                    <th>السعر/كغ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchases.slice(0, 15).map(purchase => `
                                    <tr>
                                        <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                        <td>${purchase.productType}</td>
                                        <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                                        <td>${Utils.formatCurrency(purchase.pricePerKg)}</td>
                                        <td>${Utils.formatCurrency(purchase.totalAmount)}</td>
                                        <td>
                                            <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                                ${purchase.paymentStatus || 'غير محدد'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-center">لا توجد مشتريات</p>'}
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل المورد - ${supplier.name}`);
    }

    /**
     * الحصول على قائمة الموردين للاختيار
     */
    getSuppliersForSelect() {
        return this.suppliers.map(supplier => ({
            id: supplier.id,
            name: supplier.name,
            phone: supplier.phone
        }));
    }

    /**
     * البحث عن مورد بالاسم
     */
    findSupplierByName(name) {
        return this.suppliers.find(s => s.name.toLowerCase() === name.toLowerCase());
    }

    /**
     * إضافة مورد جديد من اسم فقط (للاستخدام في المشتريات)
     */
    addSupplierByName(name) {
        const existingSupplier = this.findSupplierByName(name);
        if (existingSupplier) {
            return existingSupplier;
        }

        const supplier = {
            id: storage.generateId(this.suppliers),
            name: Utils.cleanText(name),
            phone: '',
            address: '',
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('suppliers', supplier)) {
            this.suppliers.push(supplier);
            return supplier;
        }

        return null;
    }

    /**
     * تصدير الموردين إلى CSV
     */
    exportSuppliers() {
        const suppliersData = this.suppliers.map(supplier => {
            const stats = this.calculateSupplierStats(supplier.name);
            return {
                'الرقم': supplier.id,
                'الاسم': supplier.name,
                'الهاتف': supplier.phone || '',
                'العنوان': supplier.address || '',
                'تاريخ التسجيل': Utils.formatDate(supplier.createdAt),
                'عدد المشتريات': stats.purchaseCount,
                'إجمالي المبيعات': stats.totalSales,
                'إجمالي الوزن': stats.totalWeight,
                'متوسط السعر': stats.averagePrice,
                'إجمالي الصناديق': stats.totalBoxes,
                'أنواع المنتجات': stats.products.join(', ')
            };
        });

        Utils.exportToCSV(suppliersData, `موردين_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * الحصول على أفضل الموردين
     */
    getTopSuppliers(limit = 5) {
        const suppliersWithStats = this.suppliers.map(supplier => {
            const stats = this.calculateSupplierStats(supplier.name);
            return { ...supplier, ...stats };
        });

        return suppliersWithStats
            .sort((a, b) => b.totalSales - a.totalSales)
            .slice(0, limit);
    }

    /**
     * تحديث إحصائيات الموردين في الوقت الحقيقي
     */
    updateSuppliersDisplay() {
        this.renderSuppliersTable();
    }
}

// إنشاء مثيل عام لمدير الموردين
const suppliersManager = new SuppliersManager();
