/**
 * إدارة الإعدادات
 * يحتوي على جميع وظائف إدارة إعدادات النظام والنسخ الاحتياطي
 */

class SettingsManager {
    constructor() {
        this.currentTab = 'general';
        this.settings = {};
        this.init();
    }

    /**
     * تهيئة مدير الإعدادات
     */
    init() {
        this.loadSettings();
        this.bindEvents();
        this.renderSettingsTabs();
        this.showTab('general');
    }

    /**
     * تحميل الإعدادات من التخزين
     */
    loadSettings() {
        const data = storage.loadData();
        this.settings = data?.settings || storage.getDefaultSettings();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // تبديل التبويبات
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.showTab(tabName);
            });
        });

        // حفظ الإعدادات العامة
        const generalForm = document.getElementById('general-settings-form');
        if (generalForm) {
            generalForm.addEventListener('submit', (e) => this.saveGeneralSettings(e));
        }

        // إدارة المنتجات
        const addProductBtn = document.getElementById('add-product-type');
        if (addProductBtn) {
            addProductBtn.addEventListener('click', () => this.addProductType());
        }

        // إدارة الصناديق
        const addBoxBtn = document.getElementById('add-box-type');
        if (addBoxBtn) {
            addBoxBtn.addEventListener('click', () => this.addBoxType());
        }

        // إدارة الموردين
        const addSupplierBtn = document.getElementById('add-supplier');
        if (addSupplierBtn) {
            addSupplierBtn.addEventListener('click', () => this.addSupplier());
        }

        // الأمان
        const securityForm = document.getElementById('security-settings-form');
        if (securityForm) {
            securityForm.addEventListener('submit', (e) => this.changePassword(e));
        }

        // النسخ الاحتياطي
        const createBackupBtn = document.getElementById('create-backup');
        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => this.createBackup());
        }

        const restoreBackupBtn = document.getElementById('restore-backup');
        if (restoreBackupBtn) {
            restoreBackupBtn.addEventListener('click', () => this.restoreBackup());
        }

        const backupFileInput = document.getElementById('backup-file');
        if (backupFileInput) {
            backupFileInput.addEventListener('change', (e) => this.handleBackupFile(e));
        }
    }

    /**
     * عرض التبويبات
     */
    renderSettingsTabs() {
        // ملء الإعدادات العامة
        this.fillGeneralSettings();
        
        // عرض قوائم الإدارة
        this.renderProductsList();
        this.renderBoxesList();
        this.renderSuppliersList();
        
        // عرض معلومات النسخ الاحتياطي
        this.updateBackupInfo();
    }

    /**
     * عرض تبويب معين
     */
    showTab(tabName) {
        // إخفاء جميع التبويبات
        const tabPanels = document.querySelectorAll('.tab-panel');
        tabPanels.forEach(panel => panel.classList.remove('active'));

        // إزالة الفئة النشطة من جميع الأزرار
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => btn.classList.remove('active'));

        // إظهار التبويب المحدد
        const targetPanel = document.getElementById(`${tabName}-tab`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        // تفعيل الزر المحدد
        const targetButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }

        this.currentTab = tabName;
    }

    /**
     * ملء الإعدادات العامة
     */
    fillGeneralSettings() {
        document.getElementById('market-name').value = this.settings.marketName || '';
        document.getElementById('pos-number').value = this.settings.posNumber || '';
        document.getElementById('owner-name').value = this.settings.ownerName || '';
        document.getElementById('currency').value = this.settings.currency || '';
    }

    /**
     * حفظ الإعدادات العامة
     */
    saveGeneralSettings(e) {
        e.preventDefault();
        
        const newSettings = {
            ...this.settings,
            marketName: Utils.cleanText(document.getElementById('market-name').value),
            posNumber: Utils.cleanText(document.getElementById('pos-number').value),
            ownerName: Utils.cleanText(document.getElementById('owner-name').value),
            currency: Utils.cleanText(document.getElementById('currency').value)
        };

        if (this.updateSettings(newSettings)) {
            Utils.showSuccess('تم حفظ الإعدادات بنجاح');
            
            // تحديث العرض في الهيدر
            this.updateHeaderDisplay();
        } else {
            Utils.showError('فشل في حفظ الإعدادات');
        }
    }

    /**
     * تحديث عرض الهيدر
     */
    updateHeaderDisplay() {
        // تحديث اسم السوق في الهيدر
        const titleElements = document.querySelectorAll('h1');
        titleElements.forEach(element => {
            if (element.textContent.includes('سوق الجملة')) {
                element.textContent = this.settings.marketName;
            }
        });

        // تحديث رقم نقطة البيع
        const posElements = document.querySelectorAll('.point-sale');
        posElements.forEach(element => {
            element.textContent = `نقطة بيع عدد ${this.settings.posNumber} - المالك: ${this.settings.ownerName}`;
        });
    }

    /**
     * عرض قائمة المنتجات
     */
    renderProductsList() {
        const productsList = document.getElementById('products-list');
        if (!productsList) return;

        const products = productsManager.getProductsForSelect();
        
        productsList.innerHTML = products.map(product => `
            <div class="item-row">
                <div class="item-info">
                    <strong>${product.name}</strong>
                    <small class="text-muted">${product.category}</small>
                </div>
                <div class="item-actions">
                    <button class="btn btn-sm btn-danger" onclick="settingsManager.deleteProductType('${product.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * إضافة نوع منتج جديد
     */
    addProductType() {
        const nameInput = document.getElementById('new-product-name');
        const name = Utils.cleanText(nameInput.value);
        
        if (!name) {
            Utils.showError('يرجى إدخال اسم المنتج');
            return;
        }

        const productData = {
            name: name,
            category: productsManager.getCategoryByName(name)
        };

        productsManager.addProduct(productData);
        nameInput.value = '';
        this.renderProductsList();
    }

    /**
     * حذف نوع منتج
     */
    deleteProductType(productId) {
        productsManager.deleteProduct(productId);
        this.renderProductsList();
    }

    /**
     * عرض قائمة الصناديق
     */
    renderBoxesList() {
        const boxesList = document.getElementById('boxes-list');
        if (!boxesList) return;

        const boxes = boxesManager.getBoxTypesForSelect();
        
        boxesList.innerHTML = boxes.map(box => `
            <div class="item-row">
                <div class="item-info">
                    <strong>${box.name}</strong>
                    <small class="text-muted">
                        وزن فارغ: ${Utils.formatNumber(box.emptyWeight)} كغ - 
                        حمولة: ${Utils.formatCurrency(box.loadingPrice)}
                    </small>
                </div>
                <div class="item-actions">
                    <button class="btn btn-sm btn-warning" onclick="boxesManager.editBoxType('${box.id}')">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    ${!boxesManager.isDefaultBoxType(box.name) ? `
                        <button class="btn btn-sm btn-danger" onclick="boxesManager.deleteBoxType('${box.id}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    /**
     * إضافة نوع صندوق جديد
     */
    addBoxType() {
        const nameInput = document.getElementById('new-box-name');
        const weightInput = document.getElementById('new-box-weight');
        const priceInput = document.getElementById('new-box-price');
        
        const name = Utils.cleanText(nameInput.value);
        const weight = parseFloat(weightInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        
        if (!name) {
            Utils.showError('يرجى إدخال اسم الصندوق');
            return;
        }

        const boxData = {
            name: name,
            emptyWeight: weight,
            loadingPrice: price,
            notes: ''
        };

        boxesManager.addBoxType(boxData);
        
        // مسح الحقول
        nameInput.value = '';
        weightInput.value = '';
        priceInput.value = '';
        
        this.renderBoxesList();
    }

    /**
     * عرض قائمة الموردين
     */
    renderSuppliersList() {
        const suppliersList = document.getElementById('suppliers-list');
        if (!suppliersList) return;

        const suppliers = suppliersManager.getSuppliersForSelect();
        
        suppliersList.innerHTML = suppliers.map(supplier => `
            <div class="item-row">
                <div class="item-info">
                    <strong>${supplier.name}</strong>
                    ${supplier.phone ? `<small class="text-muted">${supplier.phone}</small>` : ''}
                </div>
                <div class="item-actions">
                    <button class="btn btn-sm btn-warning" onclick="suppliersManager.showSupplierModal('${supplier.id}')">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="suppliersManager.deleteSupplier('${supplier.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * إضافة مورد جديد
     */
    addSupplier() {
        const nameInput = document.getElementById('new-supplier-name');
        const phoneInput = document.getElementById('new-supplier-phone');
        const addressInput = document.getElementById('new-supplier-address');
        
        const name = Utils.cleanText(nameInput.value);
        const phone = Utils.cleanText(phoneInput.value);
        const address = Utils.cleanText(addressInput.value);
        
        if (!name) {
            Utils.showError('يرجى إدخال اسم المورد');
            return;
        }

        const supplierData = {
            name: name,
            phone: phone,
            address: address
        };

        suppliersManager.addSupplier(supplierData);
        
        // مسح الحقول
        nameInput.value = '';
        phoneInput.value = '';
        addressInput.value = '';
        
        this.renderSuppliersList();
    }

    /**
     * تغيير كلمة المرور
     */
    changePassword(e) {
        e.preventDefault();
        
        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        
        // التحقق من كلمة المرور الحالية (يمكن تطوير هذا لاحقاً)
        const storedPassword = this.settings.password || '';
        if (storedPassword && currentPassword !== storedPassword) {
            Utils.showError('كلمة المرور الحالية غير صحيحة');
            return;
        }
        
        // التحقق من تطابق كلمة المرور الجديدة
        if (newPassword !== confirmPassword) {
            Utils.showError('كلمة المرور الجديدة غير متطابقة');
            return;
        }
        
        // التحقق من قوة كلمة المرور
        if (newPassword.length < 6) {
            Utils.showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }
        
        // حفظ كلمة المرور الجديدة
        const newSettings = {
            ...this.settings,
            password: newPassword
        };
        
        if (this.updateSettings(newSettings)) {
            Utils.showSuccess('تم تغيير كلمة المرور بنجاح');
            
            // مسح الحقول
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
        } else {
            Utils.showError('فشل في تغيير كلمة المرور');
        }
    }

    /**
     * إنشاء نسخة احتياطية
     */
    createBackup() {
        const backup = storage.createBackup();
        if (!backup) {
            Utils.showError('فشل في إنشاء النسخة الاحتياطية');
            return;
        }

        const backupData = JSON.stringify(backup, null, 2);
        const filename = `نسخة_احتياطية_${Utils.getCurrentDate()}_${new Date().getTime()}.json`;
        
        Utils.downloadFile(backupData, filename, 'application/json');
        Utils.showSuccess('تم إنشاء النسخة الاحتياطية بنجاح');
        
        this.updateBackupInfo();
    }

    /**
     * استعادة من نسخة احتياطية
     */
    restoreBackup() {
        const fileInput = document.getElementById('backup-file');
        fileInput.click();
    }

    /**
     * معالجة ملف النسخة الاحتياطية
     */
    handleBackupFile(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const backupData = JSON.parse(event.target.result);
                
                Utils.showConfirmation(
                    'هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال جميع البيانات الحالية.',
                    () => {
                        if (storage.restoreFromBackup(backupData)) {
                            Utils.showSuccess('تم استعادة النسخة الاحتياطية بنجاح');
                            
                            // إعادة تحميل الصفحة لتحديث جميع البيانات
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        } else {
                            Utils.showError('فشل في استعادة النسخة الاحتياطية');
                        }
                    }
                );
            } catch (error) {
                Utils.showError('ملف النسخة الاحتياطية غير صحيح');
            }
        };
        
        reader.readAsText(file);
        
        // مسح اختيار الملف
        e.target.value = '';
    }

    /**
     * تحديث معلومات النسخ الاحتياطي
     */
    updateBackupInfo() {
        const lastBackupElement = document.getElementById('last-backup-date');
        if (!lastBackupElement) return;

        const data = storage.loadData();
        const lastBackup = data?.lastBackup;
        
        if (lastBackup) {
            lastBackupElement.textContent = Utils.formatDateTime(lastBackup);
        } else {
            lastBackupElement.textContent = 'لم يتم إنشاء نسخة بعد';
        }
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = newSettings;
        
        const data = storage.loadData();
        data.settings = newSettings;
        
        if (storage.saveData(data)) {
            // تحديث محرك الحسابات
            calculator.updateSettings(newSettings);
            return true;
        }
        
        return false;
    }

    /**
     * إعادة تعيين الإعدادات للافتراضية
     */
    resetToDefaults() {
        Utils.showConfirmation(
            'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
            () => {
                const defaultSettings = storage.getDefaultSettings();
                if (this.updateSettings(defaultSettings)) {
                    this.fillGeneralSettings();
                    Utils.showSuccess('تم إعادة تعيين الإعدادات بنجاح');
                } else {
                    Utils.showError('فشل في إعادة تعيين الإعدادات');
                }
            }
        );
    }

    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        Utils.showConfirmation(
            'تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\nهل أنت متأكد من المتابعة؟',
            () => {
                Utils.showConfirmation(
                    'تأكيد أخير: سيتم حذف جميع المشتريات والعملاء والفواتير!\nهل تريد المتابعة؟',
                    () => {
                        if (storage.clearAllData()) {
                            Utils.showSuccess('تم مسح جميع البيانات بنجاح');
                            
                            // إعادة تحميل الصفحة
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        } else {
                            Utils.showError('فشل في مسح البيانات');
                        }
                    }
                );
            }
        );
    }

    /**
     * تصدير جميع البيانات
     */
    exportAllData() {
        const allData = storage.loadData();
        if (!allData) {
            Utils.showError('لا توجد بيانات للتصدير');
            return;
        }

        const exportData = JSON.stringify(allData, null, 2);
        const filename = `تصدير_البيانات_${Utils.getCurrentDate()}.json`;
        
        Utils.downloadFile(exportData, filename, 'application/json');
        Utils.showSuccess('تم تصدير البيانات بنجاح');
    }

    /**
     * عرض إحصائيات النظام
     */
    showSystemStats() {
        const stats = storage.getDataStatistics();
        const dataSize = JSON.stringify(storage.loadData()).length;
        const dataSizeKB = (dataSize / 1024).toFixed(2);

        const statsHTML = `
            <div class="system-stats">
                <div class="stats-header">
                    <h2><i class="fas fa-chart-pie"></i> إحصائيات النظام</h2>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <h4>العملاء</h4>
                        <span class="stat-number">${stats.customers}</span>
                    </div>
                    <div class="stat-item">
                        <h4>الموردين</h4>
                        <span class="stat-number">${stats.suppliers}</span>
                    </div>
                    <div class="stat-item">
                        <h4>المنتجات</h4>
                        <span class="stat-number">${stats.products}</span>
                    </div>
                    <div class="stat-item">
                        <h4>المشتريات</h4>
                        <span class="stat-number">${stats.purchases}</span>
                    </div>
                    <div class="stat-item">
                        <h4>الفواتير</h4>
                        <span class="stat-number">${stats.invoices}</span>
                    </div>
                    <div class="stat-item">
                        <h4>مشتريات اليوم</h4>
                        <span class="stat-number">${stats.todayPurchases}</span>
                    </div>
                    <div class="stat-item">
                        <h4>فواتير اليوم</h4>
                        <span class="stat-number">${stats.todayInvoices}</span>
                    </div>
                    <div class="stat-item">
                        <h4>حجم البيانات</h4>
                        <span class="stat-number">${dataSizeKB} KB</span>
                    </div>
                </div>

                <div class="stats-info">
                    <p><strong>آخر تعديل:</strong> ${stats.lastModified ? Utils.formatDateTime(stats.lastModified) : 'غير محدد'}</p>
                    <p><strong>آخر نسخة احتياطية:</strong> ${stats.lastBackup ? Utils.formatDateTime(stats.lastBackup) : 'لم يتم إنشاء نسخة'}</p>
                </div>
            </div>
        `;

        Utils.printHTML(statsHTML, 'إحصائيات_النظام');
    }
}

// إنشاء مثيل عام لمدير الإعدادات
const settingsManager = new SettingsManager();
