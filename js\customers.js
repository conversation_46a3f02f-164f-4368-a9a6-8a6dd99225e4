/**
 * إدارة العملاء (الحرفاء)
 * يحتوي على جميع وظائف إدارة العملاء مع البحث الذكي
 */

class CustomersManager {
    constructor() {
        this.customers = [];
        this.currentEditingId = null;
        this.init();
    }

    /**
     * تهيئة مدير العملاء
     */
    init() {
        this.loadCustomers();
        this.bindEvents();
        this.renderCustomersTable();
        this.initializeSearch();
    }

    /**
     * تحميل العملاء من التخزين
     */
    loadCustomers() {
        this.customers = storage.loadItems('customers');
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر إضافة عميل جديد
        const addCustomerBtn = document.getElementById('add-customer-btn');
        if (addCustomerBtn) {
            addCustomerBtn.addEventListener('click', () => this.showCustomerModal());
        }

        // نموذج العميل
        const customerForm = document.getElementById('customer-form');
        if (customerForm) {
            customerForm.addEventListener('submit', (e) => this.handleCustomerSubmit(e));
        }

        // إغلاق النموذج
        const modalCloses = document.querySelectorAll('.modal-close');
        modalCloses.forEach(btn => {
            btn.addEventListener('click', () => this.hideCustomerModal());
        });

        // إغلاق النموذج بالنقر خارجه
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.hideCustomerModal();
                }
            });
        }
    }

    /**
     * عرض نموذج العميل
     */
    showCustomerModal(customerId = null) {
        const modal = document.getElementById('customer-modal');
        const modalOverlay = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('customer-modal-title');
        const form = document.getElementById('customer-form');
        
        if (!modal || !modalOverlay) return;

        this.currentEditingId = customerId;
        
        if (customerId) {
            // تحرير عميل موجود
            const customer = this.customers.find(c => c.id === customerId);
            if (customer) {
                modalTitle.textContent = 'تحرير بيانات العميل';
                this.fillCustomerForm(customer);
            }
        } else {
            // إضافة عميل جديد
            modalTitle.textContent = 'إضافة عميل جديد';
            form.reset();
        }

        modalOverlay.classList.add('active');
    }

    /**
     * إخفاء نموذج العميل
     */
    hideCustomerModal() {
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
        }
        this.currentEditingId = null;
    }

    /**
     * ملء نموذج العميل بالبيانات
     */
    fillCustomerForm(customer) {
        document.getElementById('customer-name').value = customer.name || '';
        document.getElementById('customer-phone').value = customer.phone || '';
        document.getElementById('customer-address').value = customer.address || '';
    }

    /**
     * معالجة إرسال نموذج العميل
     */
    handleCustomerSubmit(e) {
        e.preventDefault();
        
        const formData = {
            name: Utils.cleanText(document.getElementById('customer-name').value),
            phone: Utils.cleanText(document.getElementById('customer-phone').value),
            address: Utils.cleanText(document.getElementById('customer-address').value)
        };

        // التحقق من صحة البيانات
        const validation = this.validateCustomerData(formData);
        if (!validation.isValid) {
            Utils.showError(validation.errors.join('\n'));
            return;
        }

        // حفظ العميل
        if (this.currentEditingId) {
            this.updateCustomer(this.currentEditingId, formData);
        } else {
            this.addCustomer(formData);
        }
    }

    /**
     * التحقق من صحة بيانات العميل
     */
    validateCustomerData(data) {
        const errors = [];

        // التحقق من الحقول المطلوبة
        if (!data.name) {
            errors.push('اسم العميل مطلوب');
        }

        // التحقق من تكرار الاسم
        const existingCustomer = this.customers.find(c => 
            c.name.toLowerCase() === data.name.toLowerCase() && 
            c.id !== this.currentEditingId
        );
        if (existingCustomer) {
            errors.push('يوجد عميل بنفس الاسم مسبقاً');
        }

        // التحقق من رقم الهاتف إذا تم إدخاله
        if (data.phone && !Utils.isValidTunisianPhone(data.phone)) {
            errors.push('رقم الهاتف غير صحيح');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * إضافة عميل جديد
     */
    addCustomer(customerData) {
        const customer = {
            ...customerData,
            id: storage.generateId(this.customers),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime(),
            totalPurchases: 0,
            totalAmount: 0,
            debts: 0,
            boxes: 0,
            deposits: 0
        };

        if (storage.saveItem('customers', customer)) {
            this.loadCustomers();
            this.renderCustomersTable();
            this.hideCustomerModal();
            Utils.showSuccess('تم إضافة العميل بنجاح');
            Utils.updateAllRealTimeData();
        } else {
            Utils.showError('فشل في إضافة العميل');
        }
    }

    /**
     * تحديث عميل موجود
     */
    updateCustomer(customerId, customerData) {
        const customer = {
            ...customerData,
            id: customerId,
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('customers', customer)) {
            this.loadCustomers();
            this.renderCustomersTable();
            this.hideCustomerModal();
            Utils.showSuccess('تم تحديث بيانات العميل بنجاح');
        } else {
            Utils.showError('فشل في تحديث بيانات العميل');
        }
    }

    /**
     * حذف عميل
     */
    deleteCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        Utils.showConfirmation(
            `هل أنت متأكد من حذف العميل "${customer.name}"؟\nسيتم حذف جميع البيانات المرتبطة به.`,
            () => {
                if (storage.deleteItem('customers', customerId)) {
                    this.loadCustomers();
                    this.renderCustomersTable();
                    Utils.showSuccess('تم حذف العميل بنجاح');
                    Utils.updateAllRealTimeData();
                } else {
                    Utils.showError('فشل في حذف العميل');
                }
            }
        );
    }

    /**
     * عرض جدول العملاء
     */
    renderCustomersTable() {
        const tbody = document.getElementById('customers-tbody');
        if (!tbody) return;

        if (this.customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا توجد عملاء مسجلين</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.customers.map((customer, index) => {
            const customerStats = this.calculateCustomerStats(customer.id);
            
            return `
                <tr>
                    <td>${index + 1}</td>
                    <td>
                        <div class="customer-name">
                            <strong>${customer.name}</strong>
                            <small class="text-muted d-block">
                                مضاف في: ${Utils.formatDate(customer.createdAt)}
                            </small>
                        </div>
                    </td>
                    <td>
                        ${customer.phone ? `
                            <a href="tel:${customer.phone}" class="phone-link">
                                <i class="fas fa-phone"></i> ${customer.phone}
                            </a>
                        ` : '<span class="text-muted">غير محدد</span>'}
                    </td>
                    <td>
                        ${customer.address ? `
                            <div class="address-text">${customer.address}</div>
                        ` : '<span class="text-muted">غير محدد</span>'}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="customersManager.showCustomerDebts('${customer.id}')">
                            <i class="fas fa-money-bill"></i>
                            ${Utils.formatCurrency(customerStats.debts)}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-warning" onclick="customersManager.showCustomerBoxes('${customer.id}')">
                            <i class="fas fa-box"></i>
                            ${customerStats.boxes}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="customersManager.showCustomerDeposits('${customer.id}')">
                            <i class="fas fa-coins"></i>
                            ${Utils.formatCurrency(customerStats.deposits)}
                        </button>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view" onclick="customersManager.viewCustomerDetails('${customer.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn edit" onclick="customersManager.showCustomerModal('${customer.id}')" title="تحرير">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete" onclick="customersManager.deleteCustomer('${customer.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * حساب إحصائيات العميل
     */
    calculateCustomerStats(customerId) {
        const purchases = storage.loadItems('purchases')
            .filter(p => p.customerId === customerId || p.supplierName === this.getCustomerName(customerId));
        
        let totalAmount = 0;
        let totalBoxes = 0;
        let totalDeposits = 0;
        let debts = 0;

        purchases.forEach(purchase => {
            totalAmount += parseFloat(purchase.totalAmount) || 0;
            totalBoxes += parseInt(purchase.boxCount) || 0;
            totalDeposits += parseFloat(purchase.deposit) || 0;
            
            // حساب الديون بناءً على حالة الدفع
            if (purchase.paymentStatus === 'غير مدفوع') {
                debts += parseFloat(purchase.totalAmount) || 0;
            } else if (purchase.paymentStatus === 'جزئي') {
                debts += (parseFloat(purchase.totalAmount) || 0) * 0.5; // افتراض 50% مدفوع
            }
        });

        return {
            totalPurchases: purchases.length,
            totalAmount,
            boxes: totalBoxes,
            deposits: totalDeposits,
            debts
        };
    }

    /**
     * الحصول على اسم العميل
     */
    getCustomerName(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        return customer ? customer.name : '';
    }

    /**
     * عرض تفاصيل العميل
     */
    viewCustomerDetails(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const stats = this.calculateCustomerStats(customerId);
        const purchases = storage.loadItems('purchases')
            .filter(p => p.customerId === customerId || p.supplierName === customer.name)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        const detailsHTML = `
            <div class="customer-details">
                <div class="customer-header">
                    <h2><i class="fas fa-user"></i> ${customer.name}</h2>
                    <div class="customer-info">
                        <p><strong>الهاتف:</strong> ${customer.phone || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                        <p><strong>تاريخ التسجيل:</strong> ${Utils.formatDateTime(customer.createdAt)}</p>
                    </div>
                </div>

                <div class="customer-stats">
                    <div class="stat-card">
                        <h4>إجمالي المشتريات</h4>
                        <span class="stat-value">${stats.totalPurchases}</span>
                    </div>
                    <div class="stat-card">
                        <h4>إجمالي المبلغ</h4>
                        <span class="stat-value">${Utils.formatCurrency(stats.totalAmount)}</span>
                    </div>
                    <div class="stat-card">
                        <h4>الديون</h4>
                        <span class="stat-value text-danger">${Utils.formatCurrency(stats.debts)}</span>
                    </div>
                    <div class="stat-card">
                        <h4>الصناديق</h4>
                        <span class="stat-value">${stats.boxes}</span>
                    </div>
                </div>

                <div class="recent-purchases">
                    <h3>المشتريات الأخيرة</h3>
                    ${purchases.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchases.slice(0, 10).map(purchase => `
                                    <tr>
                                        <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                        <td>${purchase.productType}</td>
                                        <td>${purchase.netWeight} كغ</td>
                                        <td>${Utils.formatCurrency(purchase.totalAmount)}</td>
                                        <td>
                                            <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                                ${purchase.paymentStatus || 'غير محدد'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : '<p class="text-center">لا توجد مشتريات</p>'}
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل العميل - ${customer.name}`);
    }

    /**
     * عرض ديون العميل
     */
    showCustomerDebts(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const unpaidPurchases = storage.loadItems('purchases')
            .filter(p => 
                (p.customerId === customerId || p.supplierName === customer.name) &&
                (p.paymentStatus === 'غير مدفوع' || p.paymentStatus === 'جزئي')
            );

        if (unpaidPurchases.length === 0) {
            Utils.showAlert('لا توجد ديون على هذا العميل');
            return;
        }

        const debtsHTML = `
            <div class="customer-debts">
                <h2>ديون العميل: ${customer.name}</h2>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المنتج</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>المبلغ المستحق</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${unpaidPurchases.map(purchase => {
                            const amount = parseFloat(purchase.totalAmount) || 0;
                            const dueAmount = purchase.paymentStatus === 'جزئي' ? amount * 0.5 : amount;
                            
                            return `
                                <tr>
                                    <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                    <td>${purchase.productType}</td>
                                    <td>${Utils.formatCurrency(amount)}</td>
                                    <td>
                                        <span class="status-badge status-${purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                            ${purchase.paymentStatus}
                                        </span>
                                    </td>
                                    <td class="text-danger font-weight-bold">${Utils.formatCurrency(dueAmount)}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="font-weight-bold">
                            <td colspan="4">إجمالي الديون:</td>
                            <td class="text-danger">${Utils.formatCurrency(
                                unpaidPurchases.reduce((total, purchase) => {
                                    const amount = parseFloat(purchase.totalAmount) || 0;
                                    return total + (purchase.paymentStatus === 'جزئي' ? amount * 0.5 : amount);
                                }, 0)
                            )}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;

        Utils.printHTML(debtsHTML, `ديون العميل - ${customer.name}`);
    }

    /**
     * عرض صناديق العميل
     */
    showCustomerBoxes(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const purchases = storage.loadItems('purchases')
            .filter(p => p.customerId === customerId || p.supplierName === customer.name);

        const boxesSummary = {};
        let totalBoxes = 0;

        purchases.forEach(purchase => {
            const boxType = purchase.boxType || 'غير محدد';
            const boxCount = parseInt(purchase.boxCount) || 0;
            
            if (!boxesSummary[boxType]) {
                boxesSummary[boxType] = 0;
            }
            boxesSummary[boxType] += boxCount;
            totalBoxes += boxCount;
        });

        const boxesHTML = `
            <div class="customer-boxes">
                <h2>صناديق العميل: ${customer.name}</h2>
                <div class="boxes-summary">
                    <h3>ملخص الصناديق</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>نوع الصندوق</th>
                                <th>العدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(boxesSummary).map(([type, count]) => `
                                <tr>
                                    <td>${type}</td>
                                    <td>${count}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                        <tfoot>
                            <tr class="font-weight-bold">
                                <td>إجمالي الصناديق:</td>
                                <td>${totalBoxes}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        `;

        Utils.printHTML(boxesHTML, `صناديق العميل - ${customer.name}`);
    }

    /**
     * عرض رهون العميل
     */
    showCustomerDeposits(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        const purchases = storage.loadItems('purchases')
            .filter(p => p.customerId === customerId || p.supplierName === customer.name);

        const totalDeposits = purchases.reduce((total, purchase) => {
            return total + (parseFloat(purchase.deposit) || 0);
        }, 0);

        const depositsHTML = `
            <div class="customer-deposits">
                <h2>رهون العميل: ${customer.name}</h2>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المنتج</th>
                            <th>نوع الصندوق</th>
                            <th>عدد الصناديق</th>
                            <th>الرهن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${purchases.map(purchase => `
                            <tr>
                                <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                <td>${purchase.productType}</td>
                                <td>${purchase.boxType || 'غير محدد'}</td>
                                <td>${purchase.boxCount || 0}</td>
                                <td>${Utils.formatCurrency(purchase.deposit || 0)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="font-weight-bold">
                            <td colspan="4">إجمالي الرهون:</td>
                            <td>${Utils.formatCurrency(totalDeposits)}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;

        Utils.printHTML(depositsHTML, `رهون العميل - ${customer.name}`);
    }

    /**
     * تهيئة البحث الذكي
     */
    initializeSearch() {
        const searchInput = document.getElementById('customers-search');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            this.filterCustomers(searchTerm);
        });
    }

    /**
     * تصفية العملاء
     */
    filterCustomers(searchTerm) {
        const tbody = document.getElementById('customers-tbody');
        if (!tbody) return;

        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            let found = false;
            
            // البحث في الاسم والهاتف والعنوان
            [1, 2, 3].forEach(cellIndex => {
                if (cells[cellIndex] && 
                    cells[cellIndex].textContent.toLowerCase().includes(searchTerm)) {
                    found = true;
                }
            });
            
            row.style.display = found ? '' : 'none';
        });
    }

    /**
     * تصدير العملاء إلى CSV
     */
    exportCustomers() {
        const customersData = this.customers.map(customer => {
            const stats = this.calculateCustomerStats(customer.id);
            return {
                'الرقم': customer.id,
                'الاسم': customer.name,
                'الهاتف': customer.phone || '',
                'العنوان': customer.address || '',
                'تاريخ التسجيل': Utils.formatDate(customer.createdAt),
                'عدد المشتريات': stats.totalPurchases,
                'إجمالي المبلغ': stats.totalAmount,
                'الديون': stats.debts,
                'الصناديق': stats.boxes,
                'الرهون': stats.deposits
            };
        });

        Utils.exportToCSV(customersData, `عملاء_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * الحصول على قائمة العملاء للاختيار
     */
    getCustomersForSelect() {
        return this.customers.map(customer => ({
            id: customer.id,
            name: customer.name,
            phone: customer.phone
        }));
    }
}

// إنشاء مثيل عام لمدير العملاء
const customersManager = new CustomersManager();
