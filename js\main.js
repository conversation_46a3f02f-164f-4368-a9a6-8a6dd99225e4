/**
 * الملف الرئيسي للتطبيق
 * يحتوي على التهيئة العامة وإدارة التنقل بين الأقسام
 */

class MainApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.isInitialized = false;
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    /**
     * تهيئة التطبيق بعد تحميل DOM
     */
    initializeApp() {
        try {
            // إظهار شاشة التحميل
            this.showLoadingSpinner();

            // تهيئة المكونات الأساسية
            this.initializeNavigation();
            this.initializeModals();
            this.initializeKeyboardShortcuts();
            this.initializeErrorHandling();

            // تهيئة المديرين
            this.initializeManagers();

            // إخفاء شاشة التحميل
            this.hideLoadingSpinner();

            // عرض القسم الافتراضي
            this.showSection('dashboard');

            // تحديث البيانات في الوقت الحقيقي
            this.startRealTimeUpdates();

            this.isInitialized = true;
            console.log('تم تهيئة التطبيق بنجاح');

        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            this.showError('فشل في تهيئة التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    }

    /**
     * تهيئة التنقل
     */
    initializeNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                if (section) {
                    this.showSection(section);
                }
            });
        });

        // إدارة التنقل بالتاريخ
        window.addEventListener('popstate', (e) => {
            const section = e.state?.section || 'dashboard';
            this.showSection(section, false);
        });
    }

    /**
     * عرض قسم معين
     */
    showSection(sectionName, updateHistory = true) {
        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // إزالة الفئة النشطة من جميع روابط التنقل
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // إظهار القسم المحدد
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // تفعيل رابط التنقل المحدد
        const targetNavLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (targetNavLink) {
            targetNavLink.classList.add('active');
        }

        // تحديث التاريخ
        if (updateHistory) {
            history.pushState({ section: sectionName }, '', `#${sectionName}`);
        }

        // تحديث العنوان
        this.updatePageTitle(sectionName);

        // تحديث البيانات للقسم المحدد
        this.updateSectionData(sectionName);

        this.currentSection = sectionName;
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(sectionName) {
        const titles = {
            dashboard: 'لوحة التحكم',
            customers: 'قائمة الحرفاء',
            purchases: 'قائمة المشتريات',
            suppliers: 'قائمة الموردين',
            products: 'قائمة البضائع',
            boxes: 'قائمة الصناديق',
            invoices: 'فواتير الموردين',
            settings: 'الإعدادات'
        };

        const sectionTitle = titles[sectionName] || 'سوق الجملة';
        document.title = `${sectionTitle} - سوق الجملة للخضر والغلال`;
    }

    /**
     * تحديث بيانات القسم المحدد
     */
    updateSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                if (window.dashboardManager) {
                    dashboardManager.updateDashboard();
                }
                break;
            case 'customers':
                if (window.customersManager) {
                    customersManager.renderCustomersTable();
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    purchasesManager.renderPurchasesTable();
                }
                break;
            case 'suppliers':
                if (window.suppliersManager) {
                    suppliersManager.renderSuppliersTable();
                }
                break;
            case 'products':
                if (window.productsManager) {
                    productsManager.updateProductsDisplay();
                }
                break;
            case 'boxes':
                if (window.boxesManager) {
                    boxesManager.renderBoxesTable();
                }
                break;
            case 'invoices':
                if (window.invoicesManager) {
                    invoicesManager.renderSavedInvoices();
                }
                break;
            case 'settings':
                if (window.settingsManager) {
                    settingsManager.renderSettingsTabs();
                }
                break;
        }
    }

    /**
     * تهيئة النوافذ المنبثقة
     */
    initializeModals() {
        // إغلاق النوافذ المنبثقة بالنقر خارجها
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeAllModals();
            }
        });

        // إغلاق النوافذ المنبثقة بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // أزرار إغلاق النوافذ المنبثقة
        const closeButtons = document.querySelectorAll('.modal-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.closeAllModals();
            });
        });
    }

    /**
     * إغلاق جميع النوافذ المنبثقة
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
    }

    /**
     * تهيئة اختصارات لوحة المفاتيح
     */
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // تجاهل الاختصارات إذا كان المستخدم يكتب في حقل إدخال
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                return;
            }

            // اختصارات التنقل (Ctrl + رقم)
            if (e.ctrlKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.showSection('dashboard');
                        break;
                    case '2':
                        e.preventDefault();
                        this.showSection('customers');
                        break;
                    case '3':
                        e.preventDefault();
                        this.showSection('purchases');
                        break;
                    case '4':
                        e.preventDefault();
                        this.showSection('suppliers');
                        break;
                    case '5':
                        e.preventDefault();
                        this.showSection('products');
                        break;
                    case '6':
                        e.preventDefault();
                        this.showSection('boxes');
                        break;
                    case '7':
                        e.preventDefault();
                        this.showSection('invoices');
                        break;
                    case '8':
                        e.preventDefault();
                        this.showSection('settings');
                        break;
                    case 's':
                        e.preventDefault();
                        this.quickSave();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.quickPrint();
                        break;
                }
            }

            // اختصار F5 لتحديث البيانات
            if (e.key === 'F5') {
                e.preventDefault();
                this.refreshCurrentSection();
            }
        });
    }

    /**
     * حفظ سريع
     */
    quickSave() {
        // تنفيذ حفظ سريع حسب القسم الحالي
        switch (this.currentSection) {
            case 'purchases':
                const purchaseForm = document.getElementById('purchase-form');
                if (purchaseForm && purchaseForm.style.display !== 'none') {
                    purchaseForm.dispatchEvent(new Event('submit'));
                }
                break;
            case 'settings':
                const settingsForm = document.getElementById('general-settings-form');
                if (settingsForm) {
                    settingsForm.dispatchEvent(new Event('submit'));
                }
                break;
        }
    }

    /**
     * طباعة سريعة
     */
    quickPrint() {
        switch (this.currentSection) {
            case 'dashboard':
                if (window.dashboardManager) {
                    dashboardManager.generateQuickReport();
                }
                break;
            default:
                window.print();
                break;
        }
    }

    /**
     * تحديث القسم الحالي
     */
    refreshCurrentSection() {
        this.updateSectionData(this.currentSection);
        Utils.showSuccess('تم تحديث البيانات');
    }

    /**
     * تهيئة معالجة الأخطاء
     */
    initializeErrorHandling() {
        // معالجة الأخطاء العامة
        window.addEventListener('error', (e) => {
            console.error('خطأ في التطبيق:', e.error);
            this.showError('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.');
        });

        // معالجة الأخطاء في الوعود
        window.addEventListener('unhandledrejection', (e) => {
            console.error('خطأ في Promise:', e.reason);
            this.showError('حدث خطأ في معالجة البيانات.');
        });
    }

    /**
     * تهيئة المديرين
     */
    initializeManagers() {
        // التأكد من تهيئة جميع المديرين
        if (typeof storage === 'undefined') {
            throw new Error('مدير التخزين غير متوفر');
        }

        // تهيئة المديرين بالترتيب الصحيح
        const managers = [
            'boxesManager',
            'productsManager', 
            'suppliersManager',
            'customersManager',
            'purchasesManager',
            'invoicesManager',
            'dashboardManager',
            'settingsManager'
        ];

        managers.forEach(managerName => {
            if (typeof window[managerName] === 'undefined') {
                console.warn(`${managerName} غير متوفر`);
            }
        });
    }

    /**
     * بدء التحديثات في الوقت الحقيقي
     */
    startRealTimeUpdates() {
        // تحديث الوقت كل ثانية
        setInterval(() => {
            Utils.startRealTimeClock('current-date', 'current-time');
        }, 1000);

        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                Utils.updateAllRealTimeData();
            }
        }, 30000);

        // تحديث إحصائيات الهيدر كل دقيقة
        setInterval(() => {
            Utils.updateDashboardStats();
        }, 60000);
    }

    /**
     * إظهار شاشة التحميل
     */
    showLoadingSpinner() {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.classList.add('active');
        }
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingSpinner() {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.classList.remove('active');
        }
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        Utils.showError(message);
    }

    /**
     * تصدير البيانات
     */
    exportData(type) {
        switch (type) {
            case 'customers':
                if (window.customersManager) {
                    customersManager.exportCustomers();
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    purchasesManager.exportPurchases();
                }
                break;
            case 'suppliers':
                if (window.suppliersManager) {
                    suppliersManager.exportSuppliers();
                }
                break;
            case 'products':
                if (window.productsManager) {
                    productsManager.exportProducts();
                }
                break;
            case 'boxes':
                if (window.boxesManager) {
                    boxesManager.exportBoxTypes();
                }
                break;
            case 'invoices':
                if (window.invoicesManager) {
                    invoicesManager.exportInvoices();
                }
                break;
            case 'dashboard':
                if (window.dashboardManager) {
                    dashboardManager.exportDashboardData();
                }
                break;
            case 'all':
                if (window.settingsManager) {
                    settingsManager.exportAllData();
                }
                break;
        }
    }

    /**
     * البحث العام
     */
    globalSearch(query) {
        if (!query || query.length < 2) return;

        const results = {
            customers: [],
            suppliers: [],
            products: [],
            purchases: []
        };

        // البحث في العملاء
        const customers = storage.searchItems('customers', query, ['name', 'phone', 'address']);
        results.customers = customers.slice(0, 5);

        // البحث في الموردين
        const suppliers = storage.searchItems('suppliers', query, ['name', 'phone', 'address']);
        results.suppliers = suppliers.slice(0, 5);

        // البحث في المنتجات
        const products = storage.searchItems('products', query, ['name', 'category']);
        results.products = products.slice(0, 5);

        // البحث في المشتريات
        const purchases = storage.searchItems('purchases', query, ['productType', 'supplierName', 'purchaseNumber']);
        results.purchases = purchases.slice(0, 5);

        return results;
    }

    /**
     * إعادة تشغيل التطبيق
     */
    restart() {
        Utils.showConfirmation(
            'هل تريد إعادة تشغيل التطبيق؟\nسيتم إعادة تحميل الصفحة.',
            () => {
                location.reload();
            }
        );
    }

    /**
     * معلومات التطبيق
     */
    showAbout() {
        const aboutHTML = `
            <div class="about-app">
                <div class="about-header">
                    <h2><i class="fas fa-info-circle"></i> حول التطبيق</h2>
                </div>
                
                <div class="about-content">
                    <h3>سوق الجملة للخضر والغلال - جرزونة</h3>
                    <p><strong>نقطة بيع عدد:</strong> 14</p>
                    <p><strong>المالك:</strong> عصام سولي</p>
                    
                    <hr>
                    
                    <h4>وصف التطبيق</h4>
                    <p>نظام شامل لإدارة سوق الجملة للخضر والغلال، يهدف إلى تنظيم وميكنة عمليات البيع والشراء اليومية بطريقة احترافية ودقيقة.</p>
                    
                    <h4>المميزات الرئيسية</h4>
                    <ul>
                        <li>إدارة شاملة للعملاء والموردين</li>
                        <li>تسجيل المشتريات مع حسابات تلقائية دقيقة</li>
                        <li>إنشاء فواتير الموردين مع النسب المحددة</li>
                        <li>إدارة أنواع الصناديق والحمولات</li>
                        <li>تقارير مفصلة وإحصائيات في الوقت الحقيقي</li>
                        <li>نظام نسخ احتياطي متقدم</li>
                        <li>واجهة مستخدم عربية احترافية</li>
                    </ul>
                    
                    <h4>التقنيات المستخدمة</h4>
                    <ul>
                        <li>HTML5, CSS3, JavaScript</li>
                        <li>التخزين المحلي للمتصفح</li>
                        <li>تصميم متجاوب لجميع الأجهزة</li>
                        <li>Font Awesome للأيقونات</li>
                        <li>خط Cairo للنصوص العربية</li>
                    </ul>
                    
                    <div class="version-info">
                        <p><strong>الإصدار:</strong> 1.0.0</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${Utils.formatDate(Utils.getCurrentDateTime())}</p>
                    </div>
                </div>
            </div>
        `;

        Utils.printHTML(aboutHTML, 'حول_التطبيق');
    }
}

// تهيئة التطبيق عند تحميل الصفحة
const mainApp = new MainApp();

// إضافة وظائف عامة للنافذة
window.mainApp = mainApp;
window.showSection = (section) => mainApp.showSection(section);
window.exportData = (type) => mainApp.exportData(type);
window.globalSearch = (query) => mainApp.globalSearch(query);
window.showAbout = () => mainApp.showAbout();

// معالجة الأخطاء العامة
window.onerror = function(msg, url, lineNo, columnNo, error) {
    console.error('خطأ JavaScript:', {
        message: msg,
        source: url,
        line: lineNo,
        column: columnNo,
        error: error
    });
    return false;
};

// رسالة ترحيب في وحدة التحكم
console.log(`
🌟 مرحباً بك في سوق الجملة للخضر والغلال - جرزونة
📍 نقطة بيع عدد 14
👤 المالك: عصام سولي

🚀 تم تحميل التطبيق بنجاح!
📱 للحصول على أفضل تجربة، استخدم متصفح Chrome أو Firefox
💾 جميع البيانات محفوظة محلياً في متصفحك

🔧 للمطورين:
- استخدم mainApp للوصول للتطبيق الرئيسي
- استخدم storage للوصول لمدير التخزين
- استخدم Utils للوظائف المساعدة
`);

console.log('التطبيق جاهز للاستخدام! 🎉');
