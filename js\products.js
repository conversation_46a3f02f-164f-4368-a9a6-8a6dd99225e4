/**
 * إدارة المنتجات والبضائع
 * يحتوي على جميع وظائف إدارة المنتجات والمخزون
 */

class ProductsManager {
    constructor() {
        this.products = [];
        this.defaultProducts = [
            'طماطم', 'خيار', 'فلفل أحمر', 'فلفل أخضر', 'باذنجان', 'كوسا', 'جزر', 'بصل',
            'ثوم', 'بقدونس', 'كرفس', 'نعناع', 'سبانخ', 'خس', 'ملفوف', 'قرنبيط',
            'تفاح', 'موز', 'برتقال', 'ليمون', 'عنب', 'خوخ', 'مشمش', 'كمثرى',
            'فراولة', 'كيوي', 'أناناس', 'رمان', 'تين', 'بطيخ', 'شمام', 'مانجو'
        ];
        this.init();
    }

    /**
     * تهيئة مدير المنتجات
     */
    init() {
        this.loadProducts();
        this.bindEvents();
        this.renderProductsToday();
        this.renderProductsRemaining();
        this.initializeSearch();
    }

    /**
     * تحميل المنتجات من التخزين
     */
    loadProducts() {
        this.products = storage.loadItems('products');
        // إذا لم توجد منتجات، إنشاء قائمة افتراضية
        if (this.products.length === 0) {
            this.initializeDefaultProducts();
        }
    }

    /**
     * تهيئة المنتجات الافتراضية
     */
    initializeDefaultProducts() {
        this.defaultProducts.forEach(productName => {
            const product = {
                id: storage.generateId(this.products),
                name: productName,
                category: this.getCategoryByName(productName),
                createdAt: Utils.getCurrentDateTime(),
                updatedAt: Utils.getCurrentDateTime()
            };
            this.products.push(product);
            storage.saveItem('products', product);
        });
    }

    /**
     * تحديد فئة المنتج بناءً على الاسم
     */
    getCategoryByName(name) {
        const vegetables = ['طماطم', 'خيار', 'فلفل', 'باذنجان', 'كوسا', 'جزر', 'بصل', 'ثوم', 'بقدونس', 'كرفس', 'نعناع', 'سبانخ', 'خس', 'ملفوف', 'قرنبيط'];
        const fruits = ['تفاح', 'موز', 'برتقال', 'ليمون', 'عنب', 'خوخ', 'مشمش', 'كمثرى', 'فراولة', 'كيوي', 'أناناس', 'رمان', 'تين', 'بطيخ', 'شمام', 'مانجو'];
        
        if (vegetables.some(veg => name.includes(veg))) return 'خضروات';
        if (fruits.some(fruit => name.includes(fruit))) return 'فواكه';
        return 'أخرى';
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر إضافة منتج جديد
        const addProductBtn = document.getElementById('add-product-btn');
        if (addProductBtn) {
            addProductBtn.addEventListener('click', () => this.showAddProductModal());
        }
    }

    /**
     * عرض نموذج إضافة منتج
     */
    showAddProductModal() {
        const productName = prompt('اسم المنتج الجديد:');
        if (!productName) return;

        const category = prompt('فئة المنتج (خضروات/فواكه/أخرى):', 'خضروات');
        if (!category) return;

        this.addProduct({
            name: Utils.cleanText(productName),
            category: Utils.cleanText(category)
        });
    }

    /**
     * إضافة منتج جديد
     */
    addProduct(productData) {
        // التحقق من عدم تكرار الاسم
        const existingProduct = this.products.find(p => 
            p.name.toLowerCase() === productData.name.toLowerCase()
        );
        
        if (existingProduct) {
            Utils.showError('يوجد منتج بنفس الاسم مسبقاً');
            return;
        }

        const product = {
            ...productData,
            id: storage.generateId(this.products),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('products', product)) {
            this.loadProducts();
            this.renderProductsToday();
            this.renderProductsRemaining();
            Utils.showSuccess('تم إضافة المنتج بنجاح');
        } else {
            Utils.showError('فشل في إضافة المنتج');
        }
    }

    /**
     * حذف منتج
     */
    deleteProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // التحقق من وجود مشتريات مرتبطة بالمنتج
        const relatedPurchases = storage.loadItems('purchases')
            .filter(p => p.productType === product.name);

        if (relatedPurchases.length > 0) {
            Utils.showError(`لا يمكن حذف المنتج "${product.name}" لأنه مرتبط بـ ${relatedPurchases.length} عملية شراء`);
            return;
        }

        Utils.showConfirmation(
            `هل أنت متأكد من حذف المنتج "${product.name}"؟`,
            () => {
                if (storage.deleteItem('products', productId)) {
                    this.loadProducts();
                    this.renderProductsToday();
                    this.renderProductsRemaining();
                    Utils.showSuccess('تم حذف المنتج بنجاح');
                } else {
                    Utils.showError('فشل في حذف المنتج');
                }
            }
        );
    }

    /**
     * عرض البضائع الموردة اليوم
     */
    renderProductsToday() {
        const tbody = document.getElementById('products-today-tbody');
        if (!tbody) return;

        const todayPurchases = storage.getTodayItems('purchases');
        
        if (todayPurchases.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد بضائع موردة اليوم</td>
                </tr>
            `;
            return;
        }

        // تجميع البضائع حسب النوع والمورد
        const groupedProducts = {};
        todayPurchases.forEach(purchase => {
            const key = `${purchase.productType}-${purchase.supplierName}`;
            if (!groupedProducts[key]) {
                groupedProducts[key] = {
                    productType: purchase.productType,
                    supplierName: purchase.supplierName,
                    totalBoxes: 0,
                    totalWeight: 0,
                    totalAmount: 0,
                    averagePrice: 0,
                    purchases: []
                };
            }
            
            groupedProducts[key].totalBoxes += parseInt(purchase.boxCount) || 0;
            groupedProducts[key].totalWeight += parseFloat(purchase.netWeight) || 0;
            groupedProducts[key].totalAmount += parseFloat(purchase.totalAmount) || 0;
            groupedProducts[key].purchases.push(purchase);
        });

        // حساب متوسط السعر
        Object.values(groupedProducts).forEach(group => {
            if (group.totalWeight > 0) {
                group.averagePrice = group.totalAmount / group.totalWeight;
            }
        });

        const sortedProducts = Object.values(groupedProducts)
            .sort((a, b) => b.totalAmount - a.totalAmount);

        tbody.innerHTML = sortedProducts.map(product => `
            <tr>
                <td>
                    <strong>${product.productType}</strong>
                    <small class="text-muted d-block">${this.getCategoryByName(product.productType)}</small>
                </td>
                <td class="text-center">
                    <span class="badge bg-primary">${product.totalBoxes}</span>
                </td>
                <td>
                    <div class="supplier-info">
                        <strong>${product.supplierName}</strong>
                        <small class="text-muted d-block">${product.purchases.length} عملية</small>
                    </div>
                </td>
                <td class="text-center">
                    <strong>${Utils.formatNumber(product.totalWeight)} كغ</strong>
                </td>
                <td class="text-center">
                    <strong class="text-success">${Utils.formatCurrency(product.averagePrice)}</strong>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="productsManager.viewProductDetails('${product.productType}', '${product.supplierName}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * عرض الباقي من البضائع والصناديق
     */
    renderProductsRemaining() {
        const tbody = document.getElementById('products-remaining-tbody');
        if (!tbody) return;

        const remainingInventory = calculator.calculateRemainingInventory();
        
        if (remainingInventory.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد بضائع متبقية</td>
                </tr>
            `;
            return;
        }

        // ترتيب حسب القيمة المتبقية
        remainingInventory.sort((a, b) => b.totalValue - a.totalValue);

        tbody.innerHTML = remainingInventory.map(item => `
            <tr>
                <td>
                    <div class="supplier-info">
                        <strong>${item.supplierName}</strong>
                        <small class="text-muted d-block">
                            آخر شراء: ${Utils.formatDate(item.lastPurchaseDate)}
                        </small>
                    </div>
                </td>
                <td>
                    <strong>${item.productType}</strong>
                    <small class="text-muted d-block">${this.getCategoryByName(item.productType)}</small>
                </td>
                <td class="text-center">
                    <span class="badge bg-warning">${item.totalBoxes}</span>
                </td>
                <td class="text-center">
                    <strong>${Utils.formatNumber(item.totalWeight)} كغ</strong>
                </td>
                <td class="text-center">
                    <strong class="text-primary">${Utils.formatCurrency(item.totalValue)}</strong>
                    <small class="text-muted d-block">
                        متوسط: ${Utils.formatCurrency(item.averagePrice)}/كغ
                    </small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="productsManager.viewRemainingDetails('${item.supplierName}', '${item.productType}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * عرض تفاصيل منتج معين
     */
    viewProductDetails(productType, supplierName) {
        const todayPurchases = storage.getTodayItems('purchases')
            .filter(p => p.productType === productType && p.supplierName === supplierName)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        if (todayPurchases.length === 0) {
            Utils.showAlert('لا توجد تفاصيل لهذا المنتج');
            return;
        }

        let totalBoxes = 0;
        let totalWeight = 0;
        let totalAmount = 0;

        todayPurchases.forEach(purchase => {
            totalBoxes += parseInt(purchase.boxCount) || 0;
            totalWeight += parseFloat(purchase.netWeight) || 0;
            totalAmount += parseFloat(purchase.totalAmount) || 0;
        });

        const averagePrice = totalWeight > 0 ? totalAmount / totalWeight : 0;

        const detailsHTML = `
            <div class="product-details">
                <div class="product-header">
                    <h2><i class="fas fa-apple-alt"></i> ${productType}</h2>
                    <h3>المورد: ${supplierName}</h3>
                    <p class="text-muted">البضائع الموردة اليوم - ${Utils.formatDate(Utils.getCurrentDateTime())}</p>
                </div>

                <div class="product-summary">
                    <div class="summary-card">
                        <h4>إجمالي الصناديق</h4>
                        <span class="summary-value">${totalBoxes}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الوزن</h4>
                        <span class="summary-value">${Utils.formatNumber(totalWeight)} كغ</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي المبلغ</h4>
                        <span class="summary-value">${Utils.formatCurrency(totalAmount)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط السعر</h4>
                        <span class="summary-value">${Utils.formatCurrency(averagePrice)}</span>
                    </div>
                </div>

                <div class="purchases-details">
                    <h3>تفاصيل العمليات</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>نوع الصندوق</th>
                                <th>عدد الصناديق</th>
                                <th>الوزن القائم</th>
                                <th>الوزن الصافي</th>
                                <th>السعر/كغ</th>
                                <th>المبلغ</th>
                                <th>الرهن</th>
                                <th>حالة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${todayPurchases.map(purchase => `
                                <tr>
                                    <td>${Utils.formatTime(purchase.createdAt)}</td>
                                    <td>${purchase.boxType || 'غير محدد'}</td>
                                    <td>${purchase.boxCount || 0}</td>
                                    <td>${Utils.formatNumber(purchase.grossWeight)} كغ</td>
                                    <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                                    <td>${Utils.formatCurrency(purchase.pricePerKg)}</td>
                                    <td>${Utils.formatCurrency(purchase.totalAmount)}</td>
                                    <td>${Utils.formatCurrency(purchase.deposit)}</td>
                                    <td>
                                        <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                            ${purchase.paymentStatus || 'غير محدد'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل المنتج - ${productType} - ${supplierName}`);
    }

    /**
     * عرض تفاصيل المخزون المتبقي
     */
    viewRemainingDetails(supplierName, productType) {
        const allPurchases = storage.loadItems('purchases')
            .filter(p => p.supplierName === supplierName && p.productType === productType)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        if (allPurchases.length === 0) {
            Utils.showAlert('لا توجد بيانات لهذا المنتج');
            return;
        }

        let totalBoxes = 0;
        let totalWeight = 0;
        let totalAmount = 0;

        allPurchases.forEach(purchase => {
            totalBoxes += parseInt(purchase.boxCount) || 0;
            totalWeight += parseFloat(purchase.netWeight) || 0;
            totalAmount += parseFloat(purchase.totalAmount) || 0;
        });

        const averagePrice = totalWeight > 0 ? totalAmount / totalWeight : 0;

        // تجميع حسب التاريخ
        const dailyData = {};
        allPurchases.forEach(purchase => {
            const date = Utils.formatDate(purchase.createdAt);
            if (!dailyData[date]) {
                dailyData[date] = {
                    date,
                    boxes: 0,
                    weight: 0,
                    amount: 0,
                    count: 0
                };
            }
            dailyData[date].boxes += parseInt(purchase.boxCount) || 0;
            dailyData[date].weight += parseFloat(purchase.netWeight) || 0;
            dailyData[date].amount += parseFloat(purchase.totalAmount) || 0;
            dailyData[date].count += 1;
        });

        const sortedDailyData = Object.values(dailyData)
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        const detailsHTML = `
            <div class="remaining-details">
                <div class="remaining-header">
                    <h2><i class="fas fa-warehouse"></i> المخزون المتبقي</h2>
                    <h3>${productType} - ${supplierName}</h3>
                </div>

                <div class="remaining-summary">
                    <div class="summary-card">
                        <h4>إجمالي الصناديق</h4>
                        <span class="summary-value">${totalBoxes}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الوزن</h4>
                        <span class="summary-value">${Utils.formatNumber(totalWeight)} كغ</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي القيمة</h4>
                        <span class="summary-value">${Utils.formatCurrency(totalAmount)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط السعر</h4>
                        <span class="summary-value">${Utils.formatCurrency(averagePrice)}</span>
                    </div>
                </div>

                <div class="daily-breakdown">
                    <h3>التفصيل اليومي</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>عدد العمليات</th>
                                <th>الصناديق</th>
                                <th>الوزن</th>
                                <th>المبلغ</th>
                                <th>متوسط السعر</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sortedDailyData.map(day => `
                                <tr>
                                    <td><strong>${day.date}</strong></td>
                                    <td>${day.count}</td>
                                    <td>${day.boxes}</td>
                                    <td>${Utils.formatNumber(day.weight)} كغ</td>
                                    <td>${Utils.formatCurrency(day.amount)}</td>
                                    <td>${Utils.formatCurrency(day.weight > 0 ? day.amount / day.weight : 0)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="recent-purchases">
                    <h3>آخر 10 عمليات شراء</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>الصناديق</th>
                                <th>الوزن الصافي</th>
                                <th>السعر/كغ</th>
                                <th>المبلغ</th>
                                <th>حالة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${allPurchases.slice(0, 10).map(purchase => `
                                <tr>
                                    <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                                    <td>${purchase.boxCount || 0}</td>
                                    <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                                    <td>${Utils.formatCurrency(purchase.pricePerKg)}</td>
                                    <td>${Utils.formatCurrency(purchase.totalAmount)}</td>
                                    <td>
                                        <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                            ${purchase.paymentStatus || 'غير محدد'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `المخزون المتبقي - ${productType} - ${supplierName}`);
    }

    /**
     * تهيئة البحث الذكي
     */
    initializeSearch() {
        const searchInput = document.getElementById('products-search');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            this.filterProducts(searchTerm);
        });
    }

    /**
     * تصفية المنتجات
     */
    filterProducts(searchTerm) {
        // تصفية جدول البضائع اليوم
        const todayTable = document.getElementById('products-today-table');
        if (todayTable) {
            const rows = todayTable.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                let found = false;
                
                // البحث في اسم المنتج والمورد
                [0, 2].forEach(cellIndex => {
                    if (cells[cellIndex] && 
                        cells[cellIndex].textContent.toLowerCase().includes(searchTerm)) {
                        found = true;
                    }
                });
                
                row.style.display = found ? '' : 'none';
            });
        }

        // تصفية جدول المخزون المتبقي
        const remainingTable = document.getElementById('products-remaining-table');
        if (remainingTable) {
            const rows = remainingTable.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                let found = false;
                
                // البحث في المورد واسم المنتج
                [0, 1].forEach(cellIndex => {
                    if (cells[cellIndex] && 
                        cells[cellIndex].textContent.toLowerCase().includes(searchTerm)) {
                        found = true;
                    }
                });
                
                row.style.display = found ? '' : 'none';
            });
        }
    }

    /**
     * الحصول على قائمة المنتجات للاختيار
     */
    getProductsForSelect() {
        return this.products.map(product => ({
            id: product.id,
            name: product.name,
            category: product.category
        }));
    }

    /**
     * البحث عن منتج بالاسم
     */
    findProductByName(name) {
        return this.products.find(p => p.name.toLowerCase() === name.toLowerCase());
    }

    /**
     * إضافة منتج جديد من اسم فقط (للاستخدام في المشتريات)
     */
    addProductByName(name) {
        const existingProduct = this.findProductByName(name);
        if (existingProduct) {
            return existingProduct;
        }

        const product = {
            id: storage.generateId(this.products),
            name: Utils.cleanText(name),
            category: this.getCategoryByName(name),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('products', product)) {
            this.products.push(product);
            return product;
        }

        return null;
    }

    /**
     * تصدير المنتجات إلى CSV
     */
    exportProducts() {
        const productsData = this.products.map(product => ({
            'الرقم': product.id,
            'الاسم': product.name,
            'الفئة': product.category,
            'تاريخ الإضافة': Utils.formatDate(product.createdAt)
        }));

        Utils.exportToCSV(productsData, `منتجات_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * تحديث عرض المنتجات
     */
    updateProductsDisplay() {
        this.renderProductsToday();
        this.renderProductsRemaining();
    }
}

// إنشاء مثيل عام لمدير المنتجات
const productsManager = new ProductsManager();
