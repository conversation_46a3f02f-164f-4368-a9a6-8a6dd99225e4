/**
 * وظائف مساعدة للتطبيق
 * تحتوي على وظائف التنسيق والحسابات والتحقق من البيانات
 */

class Utils {
    /**
     * تنسيق الأرقام بالعملة التونسية
     */
    static formatCurrency(amount, currency = 'د.ت') {
        const number = parseFloat(amount) || 0;
        return `${number.toFixed(3)} ${currency}`;
    }

    /**
     * تنسيق الأرقام العادية
     */
    static formatNumber(number, decimals = 2) {
        const num = parseFloat(number) || 0;
        return num.toFixed(decimals);
    }

    /**
     * تنسيق التاريخ والوقت
     */
    static formatDateTime(dateString, includeTime = true) {
        const date = new Date(dateString);
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            timeZone: 'Africa/Tunis'
        };

        if (includeTime) {
            options.hour = '2-digit';
            options.minute = '2-digit';
            options.second = '2-digit';
        }

        return date.toLocaleDateString('ar-TN', options);
    }

    /**
     * تنسيق التاريخ فقط
     */
    static formatDate(dateString) {
        return this.formatDateTime(dateString, false);
    }

    /**
     * تنسيق الوقت فقط
     */
    static formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('ar-TN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Africa/Tunis'
        });
    }

    /**
     * الحصول على التاريخ الحالي بصيغة ISO
     */
    static getCurrentDateTime() {
        return new Date().toISOString();
    }

    /**
     * الحصول على التاريخ الحالي بصيغة YYYY-MM-DD
     */
    static getCurrentDate() {
        const now = new Date();
        return now.toISOString().split('T')[0];
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * التحقق من صحة رقم الهاتف التونسي
     */
    static isValidTunisianPhone(phone) {
        const phoneRegex = /^(\+216|216|0)?[2-9]\d{7}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    /**
     * تنظيف النص من المسافات الزائدة
     */
    static cleanText(text) {
        return text ? text.trim().replace(/\s+/g, ' ') : '';
    }

    /**
     * تحويل النص إلى أحرف صغيرة للبحث
     */
    static normalizeForSearch(text) {
        return this.cleanText(text).toLowerCase();
    }

    /**
     * إنشاء رقم فاتورة تلقائي
     */
    static generateInvoiceNumber(prefix = 'INV', date = new Date()) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const time = String(date.getHours()).padStart(2, '0') + 
                    String(date.getMinutes()).padStart(2, '0');
        
        return `${prefix}-${year}${month}${day}-${time}`;
    }

    /**
     * إنشاء رقم عملية شراء تلقائي
     */
    static generatePurchaseNumber(date = new Date()) {
        return this.generateInvoiceNumber('PUR', date);
    }

    /**
     * حساب الوزن الصافي
     */
    static calculateNetWeight(grossWeight, boxType, boxCount = 1) {
        const gross = parseFloat(grossWeight) || 0;
        const emptyWeight = parseFloat(boxType.emptyWeight) || 0;
        const count = parseInt(boxCount) || 1;
        
        return Math.max(0, gross - (emptyWeight * count));
    }

    /**
     * حساب المبلغ الإجمالي
     */
    static calculateTotalAmount(netWeight, pricePerKg) {
        const weight = parseFloat(netWeight) || 0;
        const price = parseFloat(pricePerKg) || 0;
        
        return weight * price;
    }

    /**
     * حساب الرهن (حمولة الصناديق)
     */
    static calculateDeposit(boxType, boxCount, netWeight = 0) {
        const count = parseInt(boxCount) || 1;
        const loadingPrice = parseFloat(boxType.loadingPrice) || 0;
        
        // إذا كان نوع الصندوق "بلا حمولة"
        if (boxType.name === 'بلا حمولة') {
            const weight = parseFloat(netWeight) || 0;
            return weight * 10; // الوزن الصافي × 10
        }
        
        return count * loadingPrice;
    }

    /**
     * حساب النسب المئوية للفاتورة
     */
    static calculateInvoicePercentages(grossAmount, fourPercent = 4, sevenPercent = 7) {
        const gross = parseFloat(grossAmount) || 0;
        const fourPct = parseFloat(fourPercent) || 4;
        const sevenPct = parseFloat(sevenPercent) || 7;
        
        return {
            fourPercent: (gross * fourPct) / 100,
            sevenPercent: (gross * sevenPct) / 100
        };
    }

    /**
     * حساب المبلغ الصافي للفاتورة
     */
    static calculateNetInvoiceAmount(grossAmount, fourPercent, sevenPercent, loadingCost) {
        const gross = parseFloat(grossAmount) || 0;
        const four = parseFloat(fourPercent) || 0;
        const seven = parseFloat(sevenPercent) || 0;
        const loading = parseFloat(loadingCost) || 0;
        
        return gross - four - seven - loading;
    }

    /**
     * التحقق من صحة البيانات المطلوبة
     */
    static validateRequired(data, requiredFields) {
        const errors = [];
        
        requiredFields.forEach(field => {
            if (!data[field] || (typeof data[field] === 'string' && !data[field].trim())) {
                errors.push(`الحقل "${field}" مطلوب`);
            }
        });
        
        return errors;
    }

    /**
     * التحقق من صحة الأرقام
     */
    static validateNumbers(data, numberFields) {
        const errors = [];
        
        numberFields.forEach(field => {
            const value = data[field];
            if (value !== undefined && value !== null && value !== '') {
                const number = parseFloat(value);
                if (isNaN(number) || number < 0) {
                    errors.push(`الحقل "${field}" يجب أن يكون رقماً صحيحاً وأكبر من الصفر`);
                }
            }
        });
        
        return errors;
    }

    /**
     * إظهار رسالة تأكيد
     */
    static showConfirmation(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }

    /**
     * إظهار رسالة تنبيه
     */
    static showAlert(message, type = 'info') {
        // يمكن تطوير هذه الوظيفة لاحقاً لإظهار رسائل مخصصة
        alert(message);
    }

    /**
     * إظهار رسالة نجاح
     */
    static showSuccess(message) {
        this.showAlert(message, 'success');
    }

    /**
     * إظهار رسالة خطأ
     */
    static showError(message) {
        this.showAlert(message, 'error');
    }

    /**
     * تحويل البيانات إلى CSV
     */
    static exportToCSV(data, filename) {
        if (!data || data.length === 0) {
            this.showError('لا توجد بيانات للتصدير');
            return;
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => 
                `"${(row[header] || '').toString().replace(/"/g, '""')}"`
            ).join(','))
        ].join('\n');

        this.downloadFile(csvContent, filename, 'text/csv');
    }

    /**
     * تحميل ملف
     */
    static downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * طباعة محتوى HTML
     */
    static printHTML(htmlContent, title = 'طباعة') {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body { 
                        font-family: 'Cairo', Arial, sans-serif; 
                        direction: rtl; 
                        text-align: right;
                        margin: 20px;
                    }
                    table { 
                        width: 100%; 
                        border-collapse: collapse; 
                        margin: 20px 0;
                    }
                    th, td { 
                        border: 1px solid #ddd; 
                        padding: 8px; 
                        text-align: right;
                    }
                    th { 
                        background-color: #f2f2f2; 
                        font-weight: bold;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                ${htmlContent}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    /**
     * نسخ النص إلى الحافظة
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showSuccess('تم نسخ النص إلى الحافظة');
        } catch (error) {
            console.error('خطأ في نسخ النص:', error);
            this.showError('فشل في نسخ النص');
        }
    }

    /**
     * تحديث عداد الوقت الحقيقي
     */
    static startRealTimeClock(dateElementId, timeElementId) {
        const updateClock = () => {
            const now = new Date();
            
            if (dateElementId) {
                const dateElement = document.getElementById(dateElementId);
                if (dateElement) {
                    dateElement.textContent = this.formatDate(now.toISOString());
                }
            }
            
            if (timeElementId) {
                const timeElement = document.getElementById(timeElementId);
                if (timeElement) {
                    timeElement.textContent = this.formatTime(now.toISOString());
                }
            }
        };

        updateClock(); // تحديث فوري
        return setInterval(updateClock, 1000); // تحديث كل ثانية
    }

    /**
     * تحديث الإحصائيات في الوقت الحقيقي
     */
    static updateDashboardStats() {
        const stats = storage.getDataStatistics();
        
        // تحديث مبيعات اليوم
        const todaySalesElements = document.querySelectorAll('#today-sales, #dashboard-today-sales');
        todaySalesElements.forEach(element => {
            if (element) {
                element.textContent = this.formatCurrency(storage.getTodaySales());
            }
        });

        // تحديث عدد العمليات
        const todayOperationsElement = document.getElementById('today-operations');
        if (todayOperationsElement) {
            todayOperationsElement.textContent = stats.todayPurchases.toString();
        }

        // تحديث مبيعات الأمس
        const yesterdaySalesElement = document.getElementById('dashboard-yesterday-sales');
        if (yesterdaySalesElement) {
            yesterdaySalesElement.textContent = this.formatCurrency(storage.getYesterdaySales());
        }

        // تحديث إحصائيات أخرى
        const elements = {
            'dashboard-today-purchases': stats.todayPurchases,
            'dashboard-today-invoices': stats.todayInvoices,
            'dashboard-total-customers': stats.customers
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value.toString();
            }
        });
    }

    /**
     * تحديث قائمة النشاطات الأخيرة
     */
    static updateRecentActivities() {
        const activitiesContainer = document.getElementById('recent-activities');
        if (!activitiesContainer) return;

        const activities = storage.getRecentActivities(5);
        
        if (activities.length === 0) {
            activitiesContainer.innerHTML = '<p class="text-center text-secondary">لا توجد نشاطات حديثة</p>';
            return;
        }

        activitiesContainer.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-time">${this.formatDateTime(activity.time)}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث قائمة أفضل العملاء
     */
    static updateTopClients() {
        const topClientsContainer = document.getElementById('dashboard-top-clients');
        if (!topClientsContainer) return;

        const topCustomers = storage.getTopCustomers(5);
        
        if (topCustomers.length === 0) {
            topClientsContainer.innerHTML = '<p class="text-center text-secondary">لا توجد بيانات عملاء</p>';
            return;
        }

        topClientsContainer.innerHTML = topCustomers.map((customer, index) => `
            <div class="top-client-item">
                <span class="rank">${index + 1}</span>
                <div class="client-info">
                    <div class="client-name">${customer.name}</div>
                    <div class="client-stats">
                        ${customer.purchaseCount} عملية - ${this.formatCurrency(customer.totalSales)}
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث التنبيهات
     */
    static updateAlerts() {
        const alertsContainer = document.getElementById('dashboard-alerts');
        if (!alertsContainer) return;

        const alerts = [];
        
        // تحقق من المخزون المنخفض (يمكن تطويره لاحقاً)
        // تحقق من الفواتير المستحقة (يمكن تطويره لاحقاً)
        
        if (alerts.length === 0) {
            alertsContainer.innerHTML = '<p class="text-center text-success">لا توجد تنبيهات</p>';
            return;
        }

        alertsContainer.innerHTML = alerts.map(alert => `
            <div class="alert-item ${alert.type}">
                <i class="${alert.icon}"></i>
                <span>${alert.message}</span>
            </div>
        `).join('');
    }

    /**
     * تهيئة البحث الذكي
     */
    static initializeSmartSearch(inputId, tableId, searchFields) {
        const searchInput = document.getElementById(inputId);
        const table = document.getElementById(tableId);
        
        if (!searchInput || !table) return;

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                let found = false;
                
                if (searchFields && searchFields.length > 0) {
                    // البحث في حقول محددة
                    searchFields.forEach(fieldIndex => {
                        if (cells[fieldIndex] && 
                            cells[fieldIndex].textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                        }
                    });
                } else {
                    // البحث في جميع الحقول
                    cells.forEach(cell => {
                        if (cell.textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                        }
                    });
                }
                
                row.style.display = found ? '' : 'none';
            });
        });
    }

    /**
     * تحديث جميع البيانات في الوقت الحقيقي
     */
    static updateAllRealTimeData() {
        this.updateDashboardStats();
        this.updateRecentActivities();
        this.updateTopClients();
        this.updateAlerts();
    }
}

// تشغيل الساعة في الوقت الحقيقي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    Utils.startRealTimeClock('current-date', 'current-time');
    Utils.updateAllRealTimeData();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(() => {
        Utils.updateAllRealTimeData();
    }, 30000);
});
