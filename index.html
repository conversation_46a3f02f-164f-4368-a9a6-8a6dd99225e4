<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سوق الجملة للخضر والغلال - جرزونة نقطة بيع 14</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h1>سوق الجملة للخضر والغلال - جرزونة</h1>
                <span class="point-sale">نقطة بيع عدد 14</span>
            </div>
            <div class="header-info">
                <div class="date-time">
                    <span id="current-date"></span>
                    <span id="current-time"></span>
                </div>
                <div class="owner-info">
                    <span>المالك: عصام سولي</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <ul class="nav-list">
            <li><a href="#" class="nav-link active" data-section="dashboard"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="#" class="nav-link" data-section="sales"><i class="fas fa-shopping-cart"></i> المبيعات</a></li>
            <li><a href="#" class="nav-link" data-section="products"><i class="fas fa-apple-alt"></i> المنتجات</a></li>
            <li><a href="#" class="nav-link" data-section="customers"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="#" class="nav-link" data-section="inventory"><i class="fas fa-warehouse"></i> المخزون</a></li>
            <li><a href="#" class="nav-link" data-section="reports"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <li><a href="#" class="nav-link" data-section="settings"><i class="fas fa-cog"></i> الإعدادات</a></li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="dashboard-grid">
                <div class="stats-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3>مبيعات اليوم</h3>
                        <span class="stat-value" id="daily-sales">0.00 د.ت</span>
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="stat-info">
                        <h3>عدد الفواتير</h3>
                        <span class="stat-value" id="invoice-count">0</span>
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>عدد العملاء</h3>
                        <span class="stat-value" id="customer-count">0</span>
                    </div>
                </div>
                
                <div class="stats-card">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-info">
                        <h3>المنتجات المتوفرة</h3>
                        <span class="stat-value" id="product-count">0</span>
                    </div>
                </div>
            </div>

            <div class="recent-activities">
                <h2>النشاطات الأخيرة</h2>
                <div class="activity-list" id="activity-list">
                    <!-- Activities will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Sales Section -->
        <section id="sales" class="content-section">
            <div class="section-header">
                <h2>إدارة المبيعات</h2>
                <button class="btn btn-primary" id="new-sale-btn">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </button>
            </div>

            <div class="sales-container">
                <div class="invoice-form">
                    <h3>فاتورة بيع جديدة</h3>
                    <form id="sales-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>رقم الفاتورة</label>
                                <input type="text" id="invoice-number" readonly>
                            </div>
                            <div class="form-group">
                                <label>العميل</label>
                                <select id="customer-select">
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>
                        </div>

                        <div class="products-section">
                            <h4>المنتجات</h4>
                            <div class="product-row">
                                <select class="product-select">
                                    <option value="">اختر المنتج</option>
                                </select>
                                <input type="number" class="quantity-input" placeholder="الكمية" min="0" step="0.1">
                                <input type="number" class="price-input" placeholder="السعر" min="0" step="0.01">
                                <button type="button" class="btn btn-success add-product-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <div class="invoice-items" id="invoice-items">
                            <table class="items-table">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>المجموع</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="items-tbody">
                                </tbody>
                            </table>
                        </div>

                        <div class="invoice-total">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0.00 د.ت</span>
                            </div>
                            <div class="total-row">
                                <span>الضريبة (19%):</span>
                                <span id="tax">0.00 د.ت</span>
                            </div>
                            <div class="total-row final-total">
                                <span>المجموع النهائي:</span>
                                <span id="final-total">0.00 د.ت</span>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ الفاتورة</button>
                            <button type="button" class="btn btn-secondary" id="print-invoice">طباعة</button>
                            <button type="button" class="btn btn-danger" id="cancel-invoice">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="content-section">
            <div class="section-header">
                <h2>إدارة المنتجات</h2>
                <button class="btn btn-primary" id="add-product-btn">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
            </div>

            <div class="products-grid" id="products-grid">
                <!-- Products will be populated by JavaScript -->
            </div>
        </section>

        <!-- Customers Section -->
        <section id="customers" class="content-section">
            <div class="section-header">
                <h2>إدارة العملاء</h2>
                <button class="btn btn-primary" id="add-customer-btn">
                    <i class="fas fa-plus"></i> إضافة عميل
                </button>
            </div>

            <div class="customers-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customers-tbody">
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Inventory Section -->
        <section id="inventory" class="content-section">
            <div class="section-header">
                <h2>إدارة المخزون</h2>
                <button class="btn btn-primary" id="update-inventory-btn">
                    <i class="fas fa-sync"></i> تحديث المخزون
                </button>
            </div>

            <div class="inventory-grid" id="inventory-grid">
                <!-- Inventory will be populated by JavaScript -->
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
            <div class="section-header">
                <h2>التقارير</h2>
            </div>

            <div class="reports-container">
                <div class="report-filters">
                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" id="from-date">
                    </div>
                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" id="to-date">
                    </div>
                    <button class="btn btn-primary" id="generate-report">إنشاء التقرير</button>
                </div>

                <div class="report-content" id="report-content">
                    <!-- Report content will be generated here -->
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="section-header">
                <h2>الإعدادات</h2>
            </div>

            <div class="settings-container">
                <div class="settings-group">
                    <h3>إعدادات عامة</h3>
                    <div class="form-group">
                        <label>اسم المحل</label>
                        <input type="text" id="store-name" value="سوق الجملة للخضر والغلال - جرزونة">
                    </div>
                    <div class="form-group">
                        <label>رقم نقطة البيع</label>
                        <input type="text" id="pos-number" value="14">
                    </div>
                    <div class="form-group">
                        <label>اسم المالك</label>
                        <input type="text" id="owner-name" value="عصام سولي">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>إعدادات الضريبة</h3>
                    <div class="form-group">
                        <label>نسبة الضريبة (%)</label>
                        <input type="number" id="tax-rate" value="19" min="0" max="100" step="0.1">
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="btn btn-primary" id="save-settings">حفظ الإعدادات</button>
                    <button class="btn btn-secondary" id="backup-data">نسخ احتياطي للبيانات</button>
                    <button class="btn btn-warning" id="restore-data">استعادة البيانات</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay">
        <div class="modal" id="product-modal">
            <div class="modal-header">
                <h3 id="modal-title">إضافة منتج جديد</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="product-form">
                    <div class="form-group">
                        <label>اسم المنتج</label>
                        <input type="text" id="product-name" required>
                    </div>
                    <div class="form-group">
                        <label>الفئة</label>
                        <select id="product-category">
                            <option value="خضروات">خضروات</option>
                            <option value="فواكه">فواكه</option>
                            <option value="أعشاب">أعشاب</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>وحدة القياس</label>
                        <select id="product-unit">
                            <option value="كيلو">كيلو</option>
                            <option value="حبة">حبة</option>
                            <option value="حزمة">حزمة</option>
                            <option value="صندوق">صندوق</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>السعر (د.ت)</label>
                        <input type="number" id="product-price" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label>الكمية المتوفرة</label>
                        <input type="number" id="product-quantity" step="0.1" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="save-product">حفظ</button>
                <button class="btn btn-secondary modal-close">إلغاء</button>
            </div>
        </div>

        <div class="modal" id="customer-modal">
            <div class="modal-header">
                <h3>إضافة عميل جديد</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="customer-form">
                    <div class="form-group">
                        <label>اسم العميل</label>
                        <input type="text" id="customer-name" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="customer-phone">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="customer-address"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="save-customer">حفظ</button>
                <button class="btn btn-secondary modal-close">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/storage.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/products.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
