/**
 * إدارة المشتريات
 * يحتوي على جميع وظائف تسجيل وإدارة عمليات الشراء
 */

class PurchasesManager {
    constructor() {
        this.purchases = [];
        this.currentEditingId = null;
        this.init();
    }

    /**
     * تهيئة مدير المشتريات
     */
    init() {
        this.loadPurchases();
        this.bindEvents();
        this.renderPurchasesTable();
        this.populateFormSelects();
    }

    /**
     * تحميل المشتريات من التخزين
     */
    loadPurchases() {
        this.purchases = storage.loadItems('purchases');
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر إضافة مشترى جديد
        const addPurchaseBtn = document.getElementById('add-purchase-btn');
        if (addPurchaseBtn) {
            addPurchaseBtn.addEventListener('click', () => this.showPurchaseForm());
        }

        // زر إغلاق النموذج
        const closePurchaseForm = document.getElementById('close-purchase-form');
        if (closePurchaseForm) {
            closePurchaseForm.addEventListener('click', () => this.hidePurchaseForm());
        }

        // زر إلغاء
        const cancelPurchase = document.getElementById('cancel-purchase');
        if (cancelPurchase) {
            cancelPurchase.addEventListener('click', () => this.hidePurchaseForm());
        }

        // نموذج المشترى
        const purchaseForm = document.getElementById('purchase-form');
        if (purchaseForm) {
            purchaseForm.addEventListener('submit', (e) => this.handlePurchaseSubmit(e));
        }

        // حقول الحساب التلقائي
        this.bindCalculationFields();
    }

    /**
     * ربط حقول الحساب التلقائي
     */
    bindCalculationFields() {
        const grossWeightInput = document.getElementById('gross-weight');
        const boxTypeSelect = document.getElementById('box-type');
        const boxesCountInput = document.getElementById('boxes-count');
        const pricePerKgInput = document.getElementById('price-per-kg');

        if (grossWeightInput) {
            grossWeightInput.addEventListener('input', () => this.calculateFields());
        }
        if (boxTypeSelect) {
            boxTypeSelect.addEventListener('change', () => this.calculateFields());
        }
        if (boxesCountInput) {
            boxesCountInput.addEventListener('input', () => this.calculateFields());
        }
        if (pricePerKgInput) {
            pricePerKgInput.addEventListener('input', () => this.calculateFields());
        }
    }

    /**
     * حساب الحقول تلقائياً
     */
    calculateFields() {
        const grossWeight = parseFloat(document.getElementById('gross-weight').value) || 0;
        const boxTypeId = document.getElementById('box-type').value;
        const boxCount = parseInt(document.getElementById('boxes-count').value) || 0;
        const pricePerKg = parseFloat(document.getElementById('price-per-kg').value) || 0;

        if (!boxTypeId) return;

        const boxType = boxesManager.findBoxTypeById(boxTypeId);
        if (!boxType) return;

        // حساب الوزن الصافي
        const netWeight = calculator.calculateNetWeight(grossWeight, boxType, boxCount);
        document.getElementById('net-weight').value = Utils.formatNumber(netWeight);

        // حساب المبلغ الجملي
        const totalAmount = calculator.calculateTotalAmount(netWeight, pricePerKg);
        document.getElementById('total-amount').value = Utils.formatNumber(totalAmount);

        // حساب الرهن
        const deposit = calculator.calculateDeposit(boxType, boxCount, netWeight);
        document.getElementById('deposit').value = Utils.formatNumber(deposit);
    }

    /**
     * ملء قوائم الاختيار في النموذج
     */
    populateFormSelects() {
        // ملء قائمة المنتجات
        const productTypeSelect = document.getElementById('product-type');
        if (productTypeSelect) {
            const products = productsManager.getProductsForSelect();
            productTypeSelect.innerHTML = '<option value="">اختر نوع البضاعة</option>' +
                products.map(product => `<option value="${product.name}">${product.name}</option>`).join('');
        }

        // ملء قائمة الموردين
        const supplierSelect = document.getElementById('supplier-name');
        if (supplierSelect) {
            const suppliers = suppliersManager.getSuppliersForSelect();
            supplierSelect.innerHTML = '<option value="">اختر المورد</option>' +
                suppliers.map(supplier => `<option value="${supplier.name}">${supplier.name}</option>`).join('');
        }

        // ملء قائمة أنواع الصناديق
        const boxTypeSelect = document.getElementById('box-type');
        if (boxTypeSelect) {
            const boxTypes = boxesManager.getBoxTypesForSelect();
            boxTypeSelect.innerHTML = '<option value="">اختر نوع الصندوق</option>' +
                boxTypes.map(boxType => `<option value="${boxType.id}">${boxType.name}</option>`).join('');
        }
    }

    /**
     * عرض نموذج المشترى
     */
    showPurchaseForm() {
        const formContainer = document.getElementById('purchase-form-container');
        if (formContainer) {
            formContainer.style.display = 'block';
            this.populateFormSelects();
            
            // التمرير إلى النموذج
            formContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }

    /**
     * إخفاء نموذج المشترى
     */
    hidePurchaseForm() {
        const formContainer = document.getElementById('purchase-form-container');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        
        // إعادة تعيين النموذج
        const form = document.getElementById('purchase-form');
        if (form) {
            form.reset();
        }
        
        this.currentEditingId = null;
    }

    /**
     * معالجة إرسال نموذج المشترى
     */
    handlePurchaseSubmit(e) {
        e.preventDefault();
        
        const formData = this.collectFormData();
        
        // التحقق من صحة البيانات
        const validation = this.validatePurchaseData(formData);
        if (!validation.isValid) {
            Utils.showError(validation.errors.join('\n'));
            return;
        }

        // حفظ المشترى
        if (this.currentEditingId) {
            this.updatePurchase(this.currentEditingId, formData);
        } else {
            this.addPurchase(formData);
        }
    }

    /**
     * جمع بيانات النموذج
     */
    collectFormData() {
        const boxTypeId = document.getElementById('box-type').value;
        const boxType = boxesManager.findBoxTypeById(boxTypeId);
        
        return {
            productType: Utils.cleanText(document.getElementById('product-type').value),
            supplierName: Utils.cleanText(document.getElementById('supplier-name').value),
            pricePerKg: parseFloat(document.getElementById('price-per-kg').value) || 0,
            boxCount: parseInt(document.getElementById('boxes-count').value) || 0,
            boxType: boxType ? boxType.name : '',
            boxTypeId: boxTypeId,
            grossWeight: parseFloat(document.getElementById('gross-weight').value) || 0,
            netWeight: parseFloat(document.getElementById('net-weight').value) || 0,
            totalAmount: parseFloat(document.getElementById('total-amount').value) || 0,
            deposit: parseFloat(document.getElementById('deposit').value) || 0,
            paymentStatus: document.getElementById('payment-status').value
        };
    }

    /**
     * التحقق من صحة بيانات المشترى
     */
    validatePurchaseData(data) {
        const errors = [];

        // التحقق من الحقول المطلوبة
        const requiredFields = ['productType', 'supplierName', 'pricePerKg', 'boxCount', 'boxTypeId', 'grossWeight', 'paymentStatus'];
        requiredFields.forEach(field => {
            if (!data[field] || (typeof data[field] === 'string' && !data[field].trim())) {
                errors.push(`الحقل "${this.getFieldLabel(field)}" مطلوب`);
            }
        });

        // التحقق من الأرقام
        const numberFields = ['pricePerKg', 'boxCount', 'grossWeight'];
        numberFields.forEach(field => {
            if (data[field] <= 0) {
                errors.push(`الحقل "${this.getFieldLabel(field)}" يجب أن يكون أكبر من الصفر`);
            }
        });

        // التحقق من الوزن الصافي
        if (data.netWeight <= 0) {
            errors.push('الوزن الصافي يجب أن يكون أكبر من الصفر');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * الحصول على تسمية الحقل
     */
    getFieldLabel(field) {
        const labels = {
            productType: 'نوع البضاعة',
            supplierName: 'اسم المورد',
            pricePerKg: 'سعر الكيلو',
            boxCount: 'عدد الصناديق',
            boxTypeId: 'نوع الصندوق',
            grossWeight: 'الوزن القائم',
            paymentStatus: 'حالة الدفع'
        };
        return labels[field] || field;
    }

    /**
     * إضافة مشترى جديد
     */
    addPurchase(purchaseData) {
        const purchase = {
            ...purchaseData,
            id: storage.generateId(this.purchases),
            purchaseNumber: Utils.generatePurchaseNumber(),
            createdAt: Utils.getCurrentDateTime(),
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('purchases', purchase)) {
            // إضافة المورد إذا لم يكن موجوداً
            suppliersManager.addSupplierByName(purchase.supplierName);
            
            // إضافة المنتج إذا لم يكن موجوداً
            productsManager.addProductByName(purchase.productType);
            
            this.loadPurchases();
            this.renderPurchasesTable();
            this.hidePurchaseForm();
            
            Utils.showSuccess('تم تسجيل المشترى بنجاح');
            Utils.updateAllRealTimeData();
            
            // تحديث عرض الموردين والمنتجات
            suppliersManager.updateSuppliersDisplay();
            productsManager.updateProductsDisplay();
            
            // عرض خيار طباعة الوصل
            this.showReceiptOption(purchase);
        } else {
            Utils.showError('فشل في تسجيل المشترى');
        }
    }

    /**
     * تحديث مشترى موجود
     */
    updatePurchase(purchaseId, purchaseData) {
        const purchase = {
            ...purchaseData,
            id: purchaseId,
            updatedAt: Utils.getCurrentDateTime()
        };

        if (storage.saveItem('purchases', purchase)) {
            this.loadPurchases();
            this.renderPurchasesTable();
            this.hidePurchaseForm();
            
            Utils.showSuccess('تم تحديث المشترى بنجاح');
            Utils.updateAllRealTimeData();
            
            // تحديث عرض الموردين والمنتجات
            suppliersManager.updateSuppliersDisplay();
            productsManager.updateProductsDisplay();
        } else {
            Utils.showError('فشل في تحديث المشترى');
        }
    }

    /**
     * حذف مشترى
     */
    deletePurchase(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        Utils.showConfirmation(
            `هل أنت متأكد من حذف المشترى رقم "${purchase.purchaseNumber}"؟\nالمنتج: ${purchase.productType}\nالمورد: ${purchase.supplierName}`,
            () => {
                if (storage.deleteItem('purchases', purchaseId)) {
                    this.loadPurchases();
                    this.renderPurchasesTable();
                    Utils.showSuccess('تم حذف المشترى بنجاح');
                    Utils.updateAllRealTimeData();
                    
                    // تحديث عرض الموردين والمنتجات
                    suppliersManager.updateSuppliersDisplay();
                    productsManager.updateProductsDisplay();
                } else {
                    Utils.showError('فشل في حذف المشترى');
                }
            }
        );
    }

    /**
     * عرض جدول المشتريات
     */
    renderPurchasesTable() {
        const tbody = document.getElementById('purchases-tbody');
        if (!tbody) return;

        if (this.purchases.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا توجد مشتريات مسجلة</td>
                </tr>
            `;
            return;
        }

        // ترتيب المشتريات حسب التاريخ (الأحدث أولاً)
        const sortedPurchases = [...this.purchases].sort((a, b) => 
            new Date(b.createdAt) - new Date(a.createdAt)
        );

        tbody.innerHTML = sortedPurchases.map(purchase => `
            <tr>
                <td>
                    <div class="purchase-number">
                        <strong>${purchase.purchaseNumber || purchase.id}</strong>
                        <small class="text-muted d-block">
                            ${this.isToday(purchase.createdAt) ? 'اليوم' : 
                              this.isYesterday(purchase.createdAt) ? 'أمس' : 
                              Utils.formatDate(purchase.createdAt)}
                        </small>
                    </div>
                </td>
                <td>
                    <div class="purchase-datetime">
                        <strong>${Utils.formatDateTime(purchase.createdAt)}</strong>
                    </div>
                </td>
                <td>
                    <div class="supplier-info">
                        <strong>${purchase.supplierName}</strong>
                        <small class="text-muted d-block">${purchase.productType}</small>
                    </div>
                </td>
                <td class="text-center">
                    <div class="products-info">
                        <span class="badge bg-primary">${purchase.boxCount} صندوق</span>
                        <small class="text-muted d-block">${purchase.boxType}</small>
                    </div>
                </td>
                <td class="text-center">
                    <strong>${Utils.formatNumber(purchase.netWeight)} كغ</strong>
                    <small class="text-muted d-block">
                        من ${Utils.formatNumber(purchase.grossWeight)} كغ
                    </small>
                </td>
                <td class="text-center">
                    <strong class="text-success">${Utils.formatCurrency(purchase.totalAmount)}</strong>
                    <small class="text-muted d-block">
                        ${Utils.formatCurrency(purchase.pricePerKg)}/كغ
                    </small>
                </td>
                <td class="text-center">
                    <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                        ${purchase.paymentStatus || 'غير محدد'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="purchasesManager.viewPurchaseDetails('${purchase.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="purchasesManager.editPurchase('${purchase.id}')" title="تحرير">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="purchasesManager.deletePurchase('${purchase.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="action-btn" onclick="purchasesManager.printReceipt('${purchase.id}')" title="طباعة الوصل" style="background-color: var(--info-color);">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * التحقق من كون التاريخ اليوم
     */
    isToday(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }

    /**
     * التحقق من كون التاريخ أمس
     */
    isYesterday(dateString) {
        const date = new Date(dateString);
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return date.toDateString() === yesterday.toDateString();
    }

    /**
     * تحرير مشترى
     */
    editPurchase(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        this.currentEditingId = purchaseId;
        this.showPurchaseForm();
        this.fillPurchaseForm(purchase);
    }

    /**
     * ملء نموذج المشترى بالبيانات
     */
    fillPurchaseForm(purchase) {
        document.getElementById('product-type').value = purchase.productType || '';
        document.getElementById('supplier-name').value = purchase.supplierName || '';
        document.getElementById('price-per-kg').value = purchase.pricePerKg || '';
        document.getElementById('boxes-count').value = purchase.boxCount || '';
        document.getElementById('box-type').value = purchase.boxTypeId || '';
        document.getElementById('gross-weight').value = purchase.grossWeight || '';
        document.getElementById('net-weight').value = purchase.netWeight || '';
        document.getElementById('total-amount').value = purchase.totalAmount || '';
        document.getElementById('deposit').value = purchase.deposit || '';
        document.getElementById('payment-status').value = purchase.paymentStatus || '';
    }

    /**
     * عرض تفاصيل المشترى
     */
    viewPurchaseDetails(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        const boxType = boxesManager.findBoxTypeById(purchase.boxTypeId);

        const detailsHTML = `
            <div class="purchase-details">
                <div class="purchase-header">
                    <h2><i class="fas fa-shopping-cart"></i> تفاصيل المشترى</h2>
                    <h3>رقم العملية: ${purchase.purchaseNumber || purchase.id}</h3>
                    <p class="text-muted">تاريخ التسجيل: ${Utils.formatDateTime(purchase.createdAt)}</p>
                </div>

                <div class="purchase-info">
                    <div class="info-section">
                        <h4>معلومات المنتج</h4>
                        <table class="info-table">
                            <tr>
                                <td><strong>نوع البضاعة:</strong></td>
                                <td>${purchase.productType}</td>
                            </tr>
                            <tr>
                                <td><strong>المورد:</strong></td>
                                <td>${purchase.supplierName}</td>
                            </tr>
                            <tr>
                                <td><strong>سعر الكيلو:</strong></td>
                                <td>${Utils.formatCurrency(purchase.pricePerKg)}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-section">
                        <h4>معلومات الصناديق</h4>
                        <table class="info-table">
                            <tr>
                                <td><strong>نوع الصندوق:</strong></td>
                                <td>${purchase.boxType}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الصناديق:</strong></td>
                                <td>${purchase.boxCount}</td>
                            </tr>
                            <tr>
                                <td><strong>الوزن الفارغ للصندوق:</strong></td>
                                <td>${boxType ? Utils.formatNumber(boxType.emptyWeight) : 'غير محدد'} كغ</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-section">
                        <h4>معلومات الوزن والحسابات</h4>
                        <table class="info-table">
                            <tr>
                                <td><strong>الوزن القائم:</strong></td>
                                <td>${Utils.formatNumber(purchase.grossWeight)} كغ</td>
                            </tr>
                            <tr>
                                <td><strong>الوزن الصافي:</strong></td>
                                <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                            </tr>
                            <tr>
                                <td><strong>المبلغ الجملي:</strong></td>
                                <td>${Utils.formatCurrency(purchase.totalAmount)}</td>
                            </tr>
                            <tr>
                                <td><strong>الرهن:</strong></td>
                                <td>${Utils.formatCurrency(purchase.deposit)}</td>
                            </tr>
                            <tr>
                                <td><strong>حالة الدفع:</strong></td>
                                <td>
                                    <span class="status-badge status-${purchase.paymentStatus === 'كامل' ? 'paid' : purchase.paymentStatus === 'جزئي' ? 'partial' : 'unpaid'}">
                                        ${purchase.paymentStatus}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="purchase-summary">
                    <h4>ملخص العملية</h4>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي المبلغ:</span>
                            <span class="summary-value">${Utils.formatCurrency(purchase.totalAmount)}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">الرهن:</span>
                            <span class="summary-value">${Utils.formatCurrency(purchase.deposit)}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">صافي المبلغ:</span>
                            <span class="summary-value">${Utils.formatCurrency(parseFloat(purchase.totalAmount) + parseFloat(purchase.deposit))}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        Utils.printHTML(detailsHTML, `تفاصيل المشترى - ${purchase.purchaseNumber || purchase.id}`);
    }

    /**
     * عرض خيار طباعة الوصل
     */
    showReceiptOption(purchase) {
        Utils.showConfirmation(
            'تم تسجيل المشترى بنجاح!\nهل تريد طباعة وصل الاستلام؟',
            () => this.printReceipt(purchase.id)
        );
    }

    /**
     * طباعة وصل الاستلام
     */
    printReceipt(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        const settings = storage.loadData()?.settings || storage.getDefaultSettings();
        const boxType = boxesManager.findBoxTypeById(purchase.boxTypeId);

        const receiptHTML = `
            <div class="receipt">
                <div class="receipt-header">
                    <h1>${settings.marketName}</h1>
                    <h2>نقطة بيع عدد ${settings.posNumber}</h2>
                    <p>المالك: ${settings.ownerName}</p>
                    <hr>
                    <h3>وصل استلام</h3>
                </div>

                <div class="receipt-info">
                    <table class="receipt-table">
                        <tr>
                            <td><strong>رقم العملية:</strong></td>
                            <td>${purchase.purchaseNumber || purchase.id}</td>
                        </tr>
                        <tr>
                            <td><strong>التاريخ والوقت:</strong></td>
                            <td>${Utils.formatDateTime(purchase.createdAt)}</td>
                        </tr>
                        <tr>
                            <td><strong>المورد:</strong></td>
                            <td>${purchase.supplierName}</td>
                        </tr>
                        <tr>
                            <td><strong>نوع البضاعة:</strong></td>
                            <td>${purchase.productType}</td>
                        </tr>
                    </table>
                </div>

                <div class="receipt-details">
                    <h4>تفاصيل العملية</h4>
                    <table class="receipt-table">
                        <tr>
                            <td><strong>نوع الصندوق:</strong></td>
                            <td>${purchase.boxType}</td>
                        </tr>
                        <tr>
                            <td><strong>عدد الصناديق:</strong></td>
                            <td>${purchase.boxCount}</td>
                        </tr>
                        <tr>
                            <td><strong>الوزن القائم:</strong></td>
                            <td>${Utils.formatNumber(purchase.grossWeight)} كغ</td>
                        </tr>
                        <tr>
                            <td><strong>الوزن الفارغ:</strong></td>
                            <td>${boxType ? Utils.formatNumber(boxType.emptyWeight * purchase.boxCount) : 'غير محدد'} كغ</td>
                        </tr>
                        <tr>
                            <td><strong>الوزن الصافي:</strong></td>
                            <td>${Utils.formatNumber(purchase.netWeight)} كغ</td>
                        </tr>
                        <tr>
                            <td><strong>سعر الكيلو:</strong></td>
                            <td>${Utils.formatCurrency(purchase.pricePerKg)}</td>
                        </tr>
                    </table>
                </div>

                <div class="receipt-totals">
                    <table class="receipt-table">
                        <tr>
                            <td><strong>المبلغ الجملي:</strong></td>
                            <td><strong>${Utils.formatCurrency(purchase.totalAmount)}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>الرهن:</strong></td>
                            <td><strong>${Utils.formatCurrency(purchase.deposit)}</strong></td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>إجمالي المبلغ:</strong></td>
                            <td><strong>${Utils.formatCurrency(parseFloat(purchase.totalAmount) + parseFloat(purchase.deposit))}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>حالة الدفع:</strong></td>
                            <td><strong>${purchase.paymentStatus}</strong></td>
                        </tr>
                    </table>
                </div>

                <div class="receipt-footer">
                    <hr>
                    <p>شكراً لتعاملكم معنا</p>
                    <p class="print-time">طُبع في: ${Utils.formatDateTime(Utils.getCurrentDateTime())}</p>
                </div>
            </div>

            <style>
                .receipt {
                    max-width: 400px;
                    margin: 0 auto;
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                }
                .receipt-header {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .receipt-header h1 {
                    font-size: 18px;
                    margin-bottom: 5px;
                }
                .receipt-header h2 {
                    font-size: 16px;
                    margin-bottom: 5px;
                }
                .receipt-header h3 {
                    font-size: 14px;
                    margin-top: 10px;
                }
                .receipt-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }
                .receipt-table td {
                    padding: 3px 5px;
                    border-bottom: 1px dotted #ccc;
                }
                .receipt-totals .total-row td {
                    border-top: 2px solid #000;
                    border-bottom: 2px solid #000;
                    font-size: 16px;
                }
                .receipt-footer {
                    text-align: center;
                    margin-top: 20px;
                    font-size: 12px;
                }
                .print-time {
                    font-size: 10px;
                    color: #666;
                }
            </style>
        `;

        Utils.printHTML(receiptHTML, `وصل_استلام_${purchase.purchaseNumber || purchase.id}`);
    }

    /**
     * تصدير المشتريات إلى CSV
     */
    exportPurchases() {
        const purchasesData = this.purchases.map(purchase => ({
            'رقم العملية': purchase.purchaseNumber || purchase.id,
            'التاريخ': Utils.formatDateTime(purchase.createdAt),
            'المورد': purchase.supplierName,
            'نوع البضاعة': purchase.productType,
            'نوع الصندوق': purchase.boxType,
            'عدد الصناديق': purchase.boxCount,
            'الوزن القائم (كغ)': purchase.grossWeight,
            'الوزن الصافي (كغ)': purchase.netWeight,
            'سعر الكيلو (د.ت)': purchase.pricePerKg,
            'المبلغ الجملي (د.ت)': purchase.totalAmount,
            'الرهن (د.ت)': purchase.deposit,
            'حالة الدفع': purchase.paymentStatus
        }));

        Utils.exportToCSV(purchasesData, `مشتريات_${Utils.getCurrentDate()}.csv`);
    }

    /**
     * تحديث عرض المشتريات
     */
    updatePurchasesDisplay() {
        this.renderPurchasesTable();
    }
}

// إنشاء مثيل عام لمدير المشتريات
const purchasesManager = new PurchasesManager();
