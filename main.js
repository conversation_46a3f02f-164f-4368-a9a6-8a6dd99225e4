const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

class WholesaleMarketApp {
    constructor() {
        this.mainWindow = null;
        this.isQuitting = false;
        
        this.init();
    }

    init() {
        // تهيئة التطبيق
        app.whenReady().then(() => {
            this.createMainWindow();
            this.createMenu();
            this.setupEventHandlers();
        });

        // إدارة إغلاق التطبيق
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });

        app.on('before-quit', () => {
            this.isQuitting = true;
        });
    }

    createMainWindow() {
        // إنشاء النافذة الرئيسية
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 700,
            title: 'سوق الجملة للخضر والغلال - جرزونة نقطة بيع 14',
            icon: path.join(__dirname, 'assets', 'icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                preload: path.join(__dirname, 'preload.js')
            },
            show: false, // لا تظهر النافذة حتى تكتمل التهيئة
            titleBarStyle: 'default',
            backgroundColor: '#f4f6f9'
        });

        // تحميل الملف الرئيسي
        this.mainWindow.loadFile('index.html');

        // إظهار النافذة عند اكتمال التحميل
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // التركيز على النافذة
            if (process.platform === 'darwin') {
                app.dock.show();
            }
        });

        // إدارة إغلاق النافذة
        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting && process.platform === 'darwin') {
                event.preventDefault();
                this.mainWindow.hide();
                return false;
            }
        });

        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // فتح الروابط الخارجية في المتصفح الافتراضي
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });

        // إعدادات التطوير
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.webContents.openDevTools();
        }
    }

    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'نسخة احتياطية جديدة',
                        accelerator: 'CmdOrCtrl+B',
                        click: () => {
                            this.mainWindow.webContents.send('create-backup');
                        }
                    },
                    {
                        label: 'استعادة من نسخة احتياطية',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            this.showRestoreDialog();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'تصدير البيانات',
                        submenu: [
                            {
                                label: 'تصدير العملاء',
                                click: () => {
                                    this.mainWindow.webContents.send('export-data', 'customers');
                                }
                            },
                            {
                                label: 'تصدير المشتريات',
                                click: () => {
                                    this.mainWindow.webContents.send('export-data', 'purchases');
                                }
                            },
                            {
                                label: 'تصدير الموردين',
                                click: () => {
                                    this.mainWindow.webContents.send('export-data', 'suppliers');
                                }
                            },
                            {
                                label: 'تصدير جميع البيانات',
                                click: () => {
                                    this.mainWindow.webContents.send('export-data', 'all');
                                }
                            }
                        ]
                    },
                    { type: 'separator' },
                    {
                        label: 'طباعة',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => {
                            this.mainWindow.webContents.print();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    { role: 'undo', label: 'تراجع' },
                    { role: 'redo', label: 'إعادة' },
                    { type: 'separator' },
                    { role: 'cut', label: 'قص' },
                    { role: 'copy', label: 'نسخ' },
                    { role: 'paste', label: 'لصق' },
                    { role: 'selectall', label: 'تحديد الكل' }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    {
                        label: 'لوحة التحكم',
                        accelerator: 'CmdOrCtrl+1',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'dashboard');
                        }
                    },
                    {
                        label: 'قائمة الحرفاء',
                        accelerator: 'CmdOrCtrl+2',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'customers');
                        }
                    },
                    {
                        label: 'قائمة المشتريات',
                        accelerator: 'CmdOrCtrl+3',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'purchases');
                        }
                    },
                    {
                        label: 'قائمة الموردين',
                        accelerator: 'CmdOrCtrl+4',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'suppliers');
                        }
                    },
                    {
                        label: 'قائمة البضائع',
                        accelerator: 'CmdOrCtrl+5',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'products');
                        }
                    },
                    {
                        label: 'قائمة الصناديق',
                        accelerator: 'CmdOrCtrl+6',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'boxes');
                        }
                    },
                    {
                        label: 'فواتير الموردين',
                        accelerator: 'CmdOrCtrl+7',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'invoices');
                        }
                    },
                    {
                        label: 'الإعدادات',
                        accelerator: 'CmdOrCtrl+8',
                        click: () => {
                            this.mainWindow.webContents.send('show-section', 'settings');
                        }
                    },
                    { type: 'separator' },
                    { role: 'reload', label: 'إعادة تحميل' },
                    { role: 'forceReload', label: 'إعادة تحميل قسري' },
                    { role: 'toggleDevTools', label: 'أدوات المطور' },
                    { type: 'separator' },
                    { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
                    { role: 'zoomIn', label: 'تكبير' },
                    { role: 'zoomOut', label: 'تصغير' },
                    { type: 'separator' },
                    { role: 'togglefullscreen', label: 'ملء الشاشة' }
                ]
            },
            {
                label: 'نافذة',
                submenu: [
                    { role: 'minimize', label: 'تصغير' },
                    { role: 'close', label: 'إغلاق' }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول التطبيق',
                        click: () => {
                            this.showAboutDialog();
                        }
                    },
                    {
                        label: 'دليل الاستخدام',
                        click: () => {
                            this.showUserGuide();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'الإبلاغ عن مشكلة',
                        click: () => {
                            shell.openExternal('mailto:<EMAIL>?subject=مشكلة في تطبيق سوق الجملة');
                        }
                    }
                ]
            }
        ];

        // تعديل القائمة لنظام macOS
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { role: 'about', label: 'حول ' + app.getName() },
                    { type: 'separator' },
                    { role: 'services', label: 'خدمات' },
                    { type: 'separator' },
                    { role: 'hide', label: 'إخفاء ' + app.getName() },
                    { role: 'hideothers', label: 'إخفاء الآخرين' },
                    { role: 'unhide', label: 'إظهار الكل' },
                    { type: 'separator' },
                    { role: 'quit', label: 'خروج من ' + app.getName() }
                ]
            });

            // تعديل قائمة النافذة لـ macOS
            template[4].submenu = [
                { role: 'close', label: 'إغلاق' },
                { role: 'minimize', label: 'تصغير' },
                { role: 'zoom', label: 'تكبير' },
                { type: 'separator' },
                { role: 'front', label: 'إحضار الكل للمقدمة' }
            ];
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupEventHandlers() {
        // معالجة الأحداث من العملية الرئيسية
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-message-box', async (event, options) => {
            const result = await dialog.showMessageBox(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('write-file', async (event, filePath, data) => {
            try {
                fs.writeFileSync(filePath, data, 'utf8');
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('read-file', async (event, filePath) => {
            try {
                const data = fs.readFileSync(filePath, 'utf8');
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'حول التطبيق',
            message: 'سوق الجملة للخضر والغلال - جرزونة',
            detail: `نقطة بيع عدد 14
المالك: عصام سولي

الإصدار: 1.0.0
تطبيق شامل لإدارة سوق الجملة للخضر والغلال

© 2024 جميع الحقوق محفوظة`,
            buttons: ['موافق']
        });
    }

    showUserGuide() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'دليل الاستخدام',
            message: 'اختصارات لوحة المفاتيح',
            detail: `Ctrl+1: لوحة التحكم
Ctrl+2: قائمة الحرفاء
Ctrl+3: قائمة المشتريات
Ctrl+4: قائمة الموردين
Ctrl+5: قائمة البضائع
Ctrl+6: قائمة الصناديق
Ctrl+7: فواتير الموردين
Ctrl+8: الإعدادات

Ctrl+S: حفظ سريع
Ctrl+P: طباعة
Ctrl+B: نسخة احتياطية
F5: تحديث البيانات`,
            buttons: ['موافق']
        });
    }

    async showRestoreDialog() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'اختر ملف النسخة الاحتياطية',
            filters: [
                { name: 'ملفات النسخ الاحتياطي', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('restore-backup', result.filePaths[0]);
        }
    }
}

// إنشاء التطبيق
new WholesaleMarketApp();
