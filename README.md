# سوق الجملة للخضر والغلال - جرزونة

## نظام إدارة شامل لسوق الجملة - نقطة بيع عدد 14

### 🌟 المميزات الرئيسية

- **إدارة شاملة للعملاء والموردين** مع نظام بحث ذكي
- **تسجيل المشتريات** مع حسابات تلقائية دقيقة للأوزان والأسعار
- **إنشاء فواتير الموردين** مع النسب المحددة (4% و 7% والحمولة)
- **إدارة أنواع الصناديق** مع أوزانها وأسعار الحمولة
- **تقارير مفصلة وإحصائيات** في الوقت الحقيقي
- **نظام نسخ احتياطي متقدم** لحماية البيانات
- **واجهة مستخدم عربية احترافية** مع تصميم متجاوب
- **طباعة الوصولات والفواتير** بتصميم احترافي

### 🚀 التشغيل السريع

#### كتطبيق ويب:
1. افتح ملف `index.html` في متصفح حديث (Chrome, Firefox, Safari)
2. ابدأ في استخدام التطبيق مباشرة

#### كتطبيق سطح مكتب (Electron):

##### المتطلبات:
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

##### التثبيت:
```bash
# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm start
```

##### بناء التطبيق للتوزيع:
```bash
# بناء لنظام Windows
npm run build-win

# بناء لنظام macOS
npm run build-mac

# بناء لنظام Linux
npm run build-linux

# بناء لجميع الأنظمة
npm run build
```

### 📋 دليل الاستخدام

#### 1. لوحة التحكم (Dashboard)
- عرض إحصائيات المبيعات اليومية
- مقارنة الأداء مع الأمس
- أفضل العملاء والموردين
- النشاطات الأخيرة والتنبيهات

#### 2. إدارة العملاء (الحرفاء)
- إضافة وتحرير بيانات العملاء
- عرض الديون والصناديق والرهون
- البحث الذكي في قاعدة العملاء
- تقارير مفصلة لكل عميل

#### 3. تسجيل المشتريات
- تسجيل عمليات الشراء بالتفصيل
- حساب تلقائي للوزن الصافي والمبلغ الجملي
- إدارة أنواع الصناديق والرهون
- طباعة وصولات الاستلام

#### 4. إدارة الموردين
- قاعدة بيانات شاملة للموردين
- إحصائيات المبيعات لكل مورد
- تقارير الأداء والمنتجات

#### 5. إدارة البضائع
- عرض البضائع الموردة اليوم
- المخزون المتبقي حسب المورد
- تصنيف المنتجات (خضروات/فواكه)

#### 6. أنواع الصناديق
- إدارة أنواع الصناديق وأوزانها
- حساب أسعار الحمولة
- إحصائيات الاستخدام

#### 7. فواتير الموردين
- إنشاء فواتير تلقائية للموردين
- حساب النسب المحددة (4% و 7%)
- طباعة الفواتير بتصميم احترافي

### ⌨️ اختصارات لوحة المفاتيح

- `Ctrl+1`: لوحة التحكم
- `Ctrl+2`: قائمة الحرفاء
- `Ctrl+3`: قائمة المشتريات
- `Ctrl+4`: قائمة الموردين
- `Ctrl+5`: قائمة البضائع
- `Ctrl+6`: قائمة الصناديق
- `Ctrl+7`: فواتير الموردين
- `Ctrl+8`: الإعدادات
- `Ctrl+S`: حفظ سريع
- `Ctrl+P`: طباعة
- `F5`: تحديث البيانات

### 🔧 الإعدادات

#### الإعدادات العامة:
- اسم السوق ورقم نقطة البيع
- اسم المالك والعملة
- النسب المئوية للفواتير

#### إدارة البيانات:
- إضافة وحذف المنتجات
- إدارة أنواع الصناديق
- إدارة قائمة الموردين

#### النسخ الاحتياطي:
- إنشاء نسخ احتياطية تلقائية
- استعادة البيانات من نسخة احتياطية
- تصدير البيانات بصيغ مختلفة

### 💾 إدارة البيانات

#### التخزين:
- جميع البيانات محفوظة محلياً في المتصفح
- لا حاجة لاتصال بالإنترنت
- أمان كامل للبيانات الحساسة

#### النسخ الاحتياطي:
- نسخ احتياطية بصيغة JSON
- إمكانية الاستعادة الكاملة
- تصدير البيانات لبرامج أخرى

### 🖨️ الطباعة

#### الوصولات:
- وصولات استلام احترافية
- تفاصيل كاملة للعملية
- معلومات الوزن والحسابات

#### الفواتير:
- فواتير موردين مفصلة
- حسابات النسب المئوية
- تصميم احترافي للطباعة

### 🔒 الأمان

- تشفير البيانات المحلية
- نظام كلمات مرور
- حماية من فقدان البيانات
- نسخ احتياطية آمنة

### 🌐 التوافق

#### المتصفحات المدعومة:
- Google Chrome (الموصى به)
- Mozilla Firefox
- Microsoft Edge
- Safari

#### أنظمة التشغيل:
- Windows 10/11
- macOS 10.14+
- Ubuntu 18.04+

### 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +216 XX XXX XXX

### 📄 الترخيص

هذا التطبيق مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

### 🙏 شكر وتقدير

تم تطوير هذا التطبيق خصيصاً لسوق الجملة للخضر والغلال في جرزونة، نقطة بيع عدد 14، بإدارة الأستاذ عصام سولي.

---

**© 2024 سوق الجملة للخضر والغلال - جرزونة. جميع الحقوق محفوظة.**
