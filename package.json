{"name": "wholesale-market-jerzuna", "version": "1.0.0", "description": "نظام إدارة سوق الجملة للخضر والغلال - جرزونة نقطة بيع 14", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["wholesale", "market", "vegetables", "fruits", "pos", "tunisia", "arabic"], "author": {"name": "عصام سولي", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.jerzuna.wholesale-market", "productName": "سوق الجملة للخضر والغلال - جرزونة", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!.git"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": "AppImage", "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "سوق الجملة - جرزونة"}}, "repository": {"type": "git", "url": "https://github.com/oussema-souli/wholesale-market-jerzuna.git"}, "homepage": "https://github.com/oussema-souli/wholesale-market-jerzuna#readme"}