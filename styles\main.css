/* ===== CSS Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Colors */
    --primary-color: #2c5aa0;
    --primary-light: #4a7bc8;
    --primary-dark: #1e3f73;
    
    /* Secondary Colors */
    --secondary-color: #28a745;
    --secondary-light: #5cb85c;
    --secondary-dark: #1e7e34;
    
    /* Accent Colors */
    --accent-color: #ffc107;
    --accent-light: #ffcd39;
    --accent-dark: #e0a800;
    
    /* Status Colors */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #495057;
    --dark: #343a40;
    --black: #000000;
    
    /* Background Colors */
    --bg-primary: #f4f6f9;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --bg-sidebar: #2c3e50;
    
    /* Border Colors */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    
    /* Shadow */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--bg-primary);
    direction: rtl;
    text-align: right;
}

/* ===== Header Styles ===== */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-xl);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 80px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section i {
    font-size: var(--font-size-3xl);
    color: var(--accent-color);
}

.title-info h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.point-sale {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 400;
}

.header-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.datetime-display {
    text-align: center;
}

.date {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.time {
    font-size: var(--font-size-base);
    opacity: 0.9;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.stat-label {
    opacity: 0.9;
}

.stat-value {
    font-weight: 600;
    color: var(--accent-color);
}

/* ===== Sidebar Navigation ===== */
.sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    width: 280px;
    height: calc(100vh - 80px);
    background: var(--bg-sidebar);
    box-shadow: var(--shadow);
    overflow-y: auto;
    z-index: 999;
}

.nav-menu {
    list-style: none;
    padding: var(--spacing-md) 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    color: #bdc3c7;
    text-decoration: none;
    transition: var(--transition-normal);
    font-weight: 500;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-right-color: var(--accent-color);
}

.nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-right-color: var(--accent-color);
}

.nav-link i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

/* ===== Main Content ===== */
.main-content {
    margin-top: 80px;
    margin-right: 280px;
    padding: var(--spacing-xl);
    min-height: calc(100vh - 80px);
}

.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-light);
}

.section-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== Dashboard Styles ===== */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.dashboard-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition-normal);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-content {
    padding: var(--spacing-lg);
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: var(--gray);
}

.metric-value {
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

/* ===== Card Specific Colors ===== */
.sales-card .card-header {
    background: linear-gradient(135deg, var(--success-color), #20c997);
}

.operations-card .card-header {
    background: linear-gradient(135deg, var(--info-color), #20a8d8);
}

.alerts-card .card-header {
    background: linear-gradient(135deg, var(--warning-color), #ffb347);
}

.top-clients-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

/* ===== Form Styles ===== */
.form-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.close-form-btn {
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.close-form-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.purchase-form {
    padding: var(--spacing-xl);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
    background-color: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-group input[readonly] {
    background-color: var(--light-gray);
    color: var(--gray);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ===== Button Styles ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--font-family);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background-color: var(--gray);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: var(--dark-gray);
    transform: translateY(-2px);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark);
}

.btn-warning:hover {
    background-color: var(--accent-dark);
    transform: translateY(-2px);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-2px);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--white);
}

.btn-info:hover {
    background-color: #138496;
    transform: translateY(-2px);
}

/* ===== Table Styles ===== */
.table-container {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.data-table thead {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-light);
}

.data-table th {
    font-weight: 600;
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tbody tr {
    transition: var(--transition-fast);
}

.data-table tbody tr:hover {
    background-color: var(--light-gray);
}

.data-table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

/* ===== Search Box ===== */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    right: var(--spacing-md);
    color: var(--gray);
    z-index: 1;
}

.search-box input {
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-sm);
    width: 300px;
    transition: var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* ===== Modal Styles ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal.large-modal {
    max-width: 800px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* ===== Settings Tabs ===== */
.settings-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: var(--spacing-xl);
    overflow-x: auto;
}

.tab-btn {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--transition-fast);
    white-space: nowrap;
    font-family: var(--font-family);
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    position: relative;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
    display: block;
}

/* ===== Management Sections ===== */
.management-section {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
}

.add-item-form,
.add-box-form,
.add-supplier-form {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.add-item-form input,
.add-box-form input,
.add-supplier-form input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
}

.items-list {
    display: grid;
    gap: var(--spacing-sm);
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.item-row:hover {
    background-color: #e9ecef;
}

/* ===== Loading Spinner ===== */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 3000;
}

.loading-spinner.active {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--border-light);
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Status Badges ===== */
.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-paid {
    background-color: var(--success-color);
    color: var(--white);
}

.status-partial {
    background-color: var(--warning-color);
    color: var(--dark);
}

.status-unpaid {
    background-color: var(--danger-color);
    color: var(--white);
}

/* ===== Action Buttons ===== */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.action-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition-fast);
}

.action-btn.edit {
    background-color: var(--info-color);
    color: var(--white);
}

.action-btn.delete {
    background-color: var(--danger-color);
    color: var(--white);
}

.action-btn.view {
    background-color: var(--secondary-color);
    color: var(--white);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* ===== Recent Activities ===== */
.recent-activities {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.recent-activities h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.activities-list {
    display: grid;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--border-radius);
    border-right: 4px solid var(--primary-color);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.activity-icon.purchase {
    background-color: var(--success-color);
}

.activity-icon.invoice {
    background-color: var(--info-color);
}

.activity-icon.customer {
    background-color: var(--warning-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--gray);
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .main-content {
        margin-right: 250px;
    }
    
    .sidebar {
        width: 250px;
    }
}

@media (max-width: 992px) {
    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
    }
    
    .sidebar {
        transform: translateX(100%);
        transition: var(--transition-normal);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .header {
        height: auto;
        padding: var(--spacing-md);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .search-box input {
        width: 200px;
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .settings-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        text-align: center;
        border-bottom: none;
        border-left: 3px solid transparent;
    }
    
    .tab-btn.active {
        border-left-color: var(--primary-color);
        border-bottom-color: transparent;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
    
    .data-table {
        font-size: var(--font-size-xs);
    }
    
    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
    }
}

/* ===== Print Styles ===== */
@media print {
    .header,
    .sidebar,
    .section-header,
    .btn,
    .modal-overlay {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .content-section {
        display: block !important;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .table-container,
    .dashboard-card,
    .form-card {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/* ===== Utility Classes ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-bold { font-weight: 700; }
.font-weight-normal { font-weight: 400; }
.font-weight-light { font-weight: 300; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--gray); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--gray); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
