/**
 * إدارة لوحة التحكم (Dashboard)
 * يحتوي على جميع وظائف عرض الإحصائيات والتقارير الرئيسية
 */

class DashboardManager {
    constructor() {
        this.refreshInterval = null;
        this.init();
    }

    /**
     * تهيئة لوحة التحكم
     */
    init() {
        this.updateDashboard();
        this.startAutoRefresh();
        this.bindEvents();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // تحديث لوحة التحكم عند تغيير البيانات
        document.addEventListener('dataUpdated', () => {
            this.updateDashboard();
        });
    }

    /**
     * تحديث جميع بيانات لوحة التحكم
     */
    updateDashboard() {
        this.updateSalesStats();
        this.updateOperationsStats();
        this.updateAlerts();
        this.updateTopClients();
        this.updateRecentActivities();
    }

    /**
     * تحديث إحصائيات المبيعات
     */
    updateSalesStats() {
        const todaySales = storage.getTodaySales();
        const yesterdaySales = storage.getYesterdaySales();
        const monthSales = this.getMonthSales();

        // تحديث مبيعات اليوم
        const todaySalesElements = document.querySelectorAll('#today-sales, #dashboard-today-sales');
        todaySalesElements.forEach(element => {
            if (element) {
                element.textContent = Utils.formatCurrency(todaySales);
            }
        });

        // تحديث مبيعات الأمس
        const yesterdaySalesElement = document.getElementById('dashboard-yesterday-sales');
        if (yesterdaySalesElement) {
            yesterdaySalesElement.textContent = Utils.formatCurrency(yesterdaySales);
        }

        // تحديث مبيعات الشهر
        const monthSalesElement = document.getElementById('dashboard-month-sales');
        if (monthSalesElement) {
            monthSalesElement.textContent = Utils.formatCurrency(monthSales);
        }
    }

    /**
     * تحديث إحصائيات العمليات
     */
    updateOperationsStats() {
        const stats = storage.getDataStatistics();

        // تحديث مشتريات اليوم
        const todayPurchasesElement = document.getElementById('dashboard-today-purchases');
        if (todayPurchasesElement) {
            todayPurchasesElement.textContent = stats.todayPurchases.toString();
        }

        // تحديث فواتير اليوم
        const todayInvoicesElement = document.getElementById('dashboard-today-invoices');
        if (todayInvoicesElement) {
            todayInvoicesElement.textContent = stats.todayInvoices.toString();
        }

        // تحديث إجمالي العملاء
        const totalCustomersElement = document.getElementById('dashboard-total-customers');
        if (totalCustomersElement) {
            totalCustomersElement.textContent = stats.customers.toString();
        }

        // تحديث عدد العمليات في الهيدر
        const todayOperationsHeaderElement = document.getElementById('today-operations');
        if (todayOperationsHeaderElement) {
            todayOperationsHeaderElement.textContent = stats.todayPurchases.toString();
        }
    }

    /**
     * تحديث التنبيهات
     */
    updateAlerts() {
        const alertsContainer = document.getElementById('dashboard-alerts');
        if (!alertsContainer) return;

        const alerts = this.generateAlerts();
        
        if (alerts.length === 0) {
            alertsContainer.innerHTML = `
                <div class="alert-item success">
                    <i class="fas fa-check-circle"></i>
                    <span>لا توجد تنبيهات</span>
                </div>
            `;
            return;
        }

        alertsContainer.innerHTML = alerts.map(alert => `
            <div class="alert-item ${alert.type}">
                <i class="${alert.icon}"></i>
                <span>${alert.message}</span>
            </div>
        `).join('');
    }

    /**
     * إنشاء التنبيهات
     */
    generateAlerts() {
        const alerts = [];
        
        // تنبيه المبيعات المنخفضة
        const todaySales = storage.getTodaySales();
        const yesterdaySales = storage.getYesterdaySales();
        
        if (yesterdaySales > 0 && todaySales < yesterdaySales * 0.5) {
            alerts.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                message: 'مبيعات اليوم أقل من 50% من مبيعات الأمس'
            });
        }

        // تنبيه عدم وجود مشتريات اليوم
        const todayPurchases = storage.getTodayItems('purchases');
        if (todayPurchases.length === 0) {
            alerts.push({
                type: 'info',
                icon: 'fas fa-info-circle',
                message: 'لم يتم تسجيل أي مشتريات اليوم'
            });
        }

        // تنبيه الديون المستحقة
        const unpaidPurchases = storage.loadItems('purchases')
            .filter(p => p.paymentStatus === 'غير مدفوع' || p.paymentStatus === 'جزئي');
        
        if (unpaidPurchases.length > 0) {
            const totalDebts = unpaidPurchases.reduce((total, purchase) => {
                const amount = parseFloat(purchase.totalAmount) || 0;
                return total + (purchase.paymentStatus === 'جزئي' ? amount * 0.5 : amount);
            }, 0);

            alerts.push({
                type: 'danger',
                icon: 'fas fa-money-bill-wave',
                message: `يوجد ${unpaidPurchases.length} عملية غير مدفوعة بقيمة ${Utils.formatCurrency(totalDebts)}`
            });
        }

        // تنبيه عدم وجود فواتير اليوم
        const todayInvoices = storage.getTodayItems('invoices');
        if (todayInvoices.length === 0 && todayPurchases.length > 0) {
            alerts.push({
                type: 'warning',
                icon: 'fas fa-file-invoice',
                message: 'لم يتم إنشاء فواتير للموردين اليوم'
            });
        }

        return alerts;
    }

    /**
     * تحديث أفضل العملاء
     */
    updateTopClients() {
        const topClientsContainer = document.getElementById('dashboard-top-clients');
        if (!topClientsContainer) return;

        const topCustomers = storage.getTopCustomers(5);
        
        if (topCustomers.length === 0) {
            topClientsContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-users"></i>
                    <p>لا توجد بيانات عملاء</p>
                </div>
            `;
            return;
        }

        topClientsContainer.innerHTML = topCustomers.map((customer, index) => `
            <div class="top-client-item">
                <div class="client-rank">
                    <span class="rank-number">${index + 1}</span>
                </div>
                <div class="client-info">
                    <div class="client-name">${customer.name}</div>
                    <div class="client-stats">
                        <span class="purchases-count">${customer.purchaseCount} عملية</span>
                        <span class="total-amount">${Utils.formatCurrency(customer.totalSales)}</span>
                    </div>
                </div>
                <div class="client-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="customersManager.viewCustomerDetails('${this.getCustomerIdByName(customer.name)}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث النشاطات الأخيرة
     */
    updateRecentActivities() {
        const activitiesContainer = document.getElementById('recent-activities');
        if (!activitiesContainer) return;

        const activities = storage.getRecentActivities(8);
        
        if (activities.length === 0) {
            activitiesContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-history"></i>
                    <p>لا توجد نشاطات حديثة</p>
                </div>
            `;
            return;
        }

        activitiesContainer.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-time">${this.getRelativeTime(activity.time)}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * الحصول على مبيعات الشهر الحالي
     */
    getMonthSales() {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        return storage.calculateTotalSales(
            startOfMonth.toISOString().split('T')[0],
            endOfMonth.toISOString().split('T')[0]
        );
    }

    /**
     * الحصول على معرف العميل بالاسم
     */
    getCustomerIdByName(name) {
        const customers = storage.loadItems('customers');
        const customer = customers.find(c => c.name === name);
        return customer ? customer.id : null;
    }

    /**
     * الحصول على الوقت النسبي
     */
    getRelativeTime(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) {
            return 'الآن';
        } else if (diffInMinutes < 60) {
            return `منذ ${diffInMinutes} دقيقة`;
        } else if (diffInMinutes < 1440) { // 24 hours
            const hours = Math.floor(diffInMinutes / 60);
            return `منذ ${hours} ساعة`;
        } else {
            const days = Math.floor(diffInMinutes / 1440);
            return `منذ ${days} يوم`;
        }
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh() {
        // تحديث كل 30 ثانية
        this.refreshInterval = setInterval(() => {
            this.updateDashboard();
        }, 30000);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * إنشاء تقرير سريع
     */
    generateQuickReport() {
        const today = Utils.getCurrentDate();
        const todayPurchases = storage.getTodayItems('purchases');
        const todayInvoices = storage.getTodayItems('invoices');
        const todaySales = storage.getTodaySales();
        
        // حساب إحصائيات سريعة
        let totalWeight = 0;
        let totalBoxes = 0;
        let totalDeposits = 0;
        const suppliers = new Set();
        const products = new Set();

        todayPurchases.forEach(purchase => {
            totalWeight += parseFloat(purchase.netWeight) || 0;
            totalBoxes += parseInt(purchase.boxCount) || 0;
            totalDeposits += parseFloat(purchase.deposit) || 0;
            suppliers.add(purchase.supplierName);
            products.add(purchase.productType);
        });

        const reportHTML = `
            <div class="quick-report">
                <div class="report-header">
                    <h2><i class="fas fa-chart-line"></i> التقرير السريع</h2>
                    <h3>تاريخ: ${Utils.formatDate(today)}</h3>
                </div>

                <div class="report-summary">
                    <div class="summary-grid">
                        <div class="summary-card">
                            <h4>إجمالي المبيعات</h4>
                            <span class="summary-value">${Utils.formatCurrency(todaySales)}</span>
                        </div>
                        <div class="summary-card">
                            <h4>عدد المشتريات</h4>
                            <span class="summary-value">${todayPurchases.length}</span>
                        </div>
                        <div class="summary-card">
                            <h4>عدد الفواتير</h4>
                            <span class="summary-value">${todayInvoices.length}</span>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الوزن</h4>
                            <span class="summary-value">${Utils.formatNumber(totalWeight)} كغ</span>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الصناديق</h4>
                            <span class="summary-value">${totalBoxes}</span>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الرهون</h4>
                            <span class="summary-value">${Utils.formatCurrency(totalDeposits)}</span>
                        </div>
                        <div class="summary-card">
                            <h4>عدد الموردين</h4>
                            <span class="summary-value">${suppliers.size}</span>
                        </div>
                        <div class="summary-card">
                            <h4>أنواع المنتجات</h4>
                            <span class="summary-value">${products.size}</span>
                        </div>
                    </div>
                </div>

                <div class="report-details">
                    <div class="suppliers-section">
                        <h4>الموردين النشطين اليوم</h4>
                        <ul>
                            ${Array.from(suppliers).map(supplier => `<li>${supplier}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="products-section">
                        <h4>المنتجات المتداولة اليوم</h4>
                        <ul>
                            ${Array.from(products).map(product => `<li>${product}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="report-footer">
                    <p>تم إنشاء التقرير في: ${Utils.formatDateTime(Utils.getCurrentDateTime())}</p>
                </div>
            </div>
        `;

        Utils.printHTML(reportHTML, `التقرير_السريع_${today}`);
    }

    /**
     * مقارنة الأداء مع الأمس
     */
    compareWithYesterday() {
        const todaySales = storage.getTodaySales();
        const yesterdaySales = storage.getYesterdaySales();
        const todayPurchases = storage.getTodayItems('purchases').length;
        const yesterdayPurchases = storage.getYesterdayItems('purchases').length;

        const salesChange = yesterdaySales > 0 ? ((todaySales - yesterdaySales) / yesterdaySales) * 100 : 0;
        const purchasesChange = yesterdayPurchases > 0 ? ((todayPurchases - yesterdayPurchases) / yesterdayPurchases) * 100 : 0;

        const comparisonHTML = `
            <div class="performance-comparison">
                <div class="comparison-header">
                    <h2><i class="fas fa-chart-bar"></i> مقارنة الأداء</h2>
                    <h3>اليوم مقابل الأمس</h3>
                </div>

                <div class="comparison-grid">
                    <div class="comparison-card">
                        <h4>المبيعات</h4>
                        <div class="comparison-values">
                            <div class="today-value">
                                <span class="label">اليوم:</span>
                                <span class="value">${Utils.formatCurrency(todaySales)}</span>
                            </div>
                            <div class="yesterday-value">
                                <span class="label">الأمس:</span>
                                <span class="value">${Utils.formatCurrency(yesterdaySales)}</span>
                            </div>
                            <div class="change-value ${salesChange >= 0 ? 'positive' : 'negative'}">
                                <i class="fas fa-${salesChange >= 0 ? 'arrow-up' : 'arrow-down'}"></i>
                                <span>${Math.abs(salesChange).toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-card">
                        <h4>عدد المشتريات</h4>
                        <div class="comparison-values">
                            <div class="today-value">
                                <span class="label">اليوم:</span>
                                <span class="value">${todayPurchases}</span>
                            </div>
                            <div class="yesterday-value">
                                <span class="label">الأمس:</span>
                                <span class="value">${yesterdayPurchases}</span>
                            </div>
                            <div class="change-value ${purchasesChange >= 0 ? 'positive' : 'negative'}">
                                <i class="fas fa-${purchasesChange >= 0 ? 'arrow-up' : 'arrow-down'}"></i>
                                <span>${Math.abs(purchasesChange).toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comparison-summary">
                    <h4>الملخص</h4>
                    <p>
                        ${salesChange >= 0 ? 
                            `المبيعات اليوم أعلى من الأمس بنسبة ${salesChange.toFixed(1)}%` :
                            `المبيعات اليوم أقل من الأمس بنسبة ${Math.abs(salesChange).toFixed(1)}%`
                        }
                    </p>
                    <p>
                        ${purchasesChange >= 0 ? 
                            `عدد المشتريات اليوم أعلى من الأمس بنسبة ${purchasesChange.toFixed(1)}%` :
                            `عدد المشتريات اليوم أقل من الأمس بنسبة ${Math.abs(purchasesChange).toFixed(1)}%`
                        }
                    </p>
                </div>
            </div>
        `;

        Utils.printHTML(comparisonHTML, `مقارنة_الأداء_${Utils.getCurrentDate()}`);
    }

    /**
     * تصدير بيانات لوحة التحكم
     */
    exportDashboardData() {
        const today = Utils.getCurrentDate();
        const todayPurchases = storage.getTodayItems('purchases');
        const todayInvoices = storage.getTodayItems('invoices');
        
        const dashboardData = {
            تاريخ_التقرير: today,
            إجمالي_المبيعات: storage.getTodaySales(),
            عدد_المشتريات: todayPurchases.length,
            عدد_الفواتير: todayInvoices.length,
            إجمالي_العملاء: storage.getDataStatistics().customers,
            مبيعات_الأمس: storage.getYesterdaySales(),
            مبيعات_الشهر: this.getMonthSales()
        };

        Utils.exportToCSV([dashboardData], `لوحة_التحكم_${today}.csv`);
    }
}

// إنشاء مثيل عام لمدير لوحة التحكم
const dashboardManager = new DashboardManager();
